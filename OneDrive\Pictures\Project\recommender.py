import pandas as pd
import ast # Used to safely evaluate string-formatted lists/dicts
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import linear_kernel
from sklearn.neighbors import NearestNeighbors

# --- 1. Load The Movie Data ---
try:
   movies_df = pd.read_csv('tmdb_5000_movies.csv')
   credits_df = pd.read_csv('tmdb_5000_credits.csv')
except FileNotFoundError:
    print("Error: Make sure 'tmdb_5000_movies.csv' and 'tmdb_5000_credits.csv' are in the same folder as the script.")
    exit()

movies_df = movies_df.merge(credits_df, on='title')

# --- 2. Data Cleaning and Preprocessing ---
# (This section is largely the same as before)

def parse_features(feature_list_str):
    try:
        return [item['name'] for item in ast.literal_eval(feature_list_str)]
    except (ValueError, SyntaxError):
        return []

# Parse cast information to get actor names
def parse_cast(cast_str, limit=3):
    try:
        cast_list = ast.literal_eval(cast_str)
        return [item['name'] for item in cast_list[:limit]]  # Get top 3 actors
    except (ValueError, SyntaxError):
        return []

# Parse crew information to get director name
def parse_director(crew_str):
    try:
        crew_list = ast.literal_eval(crew_str)
        directors = [item['name'] for item in crew_list if item['job'] == 'Director']
        return directors if directors else []
    except (ValueError, SyntaxError):
        return []

for feature in ['genres', 'keywords']:
    movies_df[feature] = movies_df[feature].apply(parse_features)

# Extract cast and director information
movies_df['cast_names'] = movies_df['cast'].apply(parse_cast)
movies_df['director'] = movies_df['crew'].apply(parse_director)

movies_df['overview'] = movies_df['overview'].fillna('')

# --- 3. Feature Engineering for "Similar Movies" ---
def create_soup(x):
    # Enhanced soup with cast and director information
    genres = ' '.join([g.replace(" ", "") for g in x['genres']])
    keywords = ' '.join([k.replace(" ", "") for k in x['keywords']])
    cast = ' '.join([c.replace(" ", "") for c in x['cast_names']]) if len(x['cast_names']) > 0 else ''
    director = ' '.join([d.replace(" ", "") for d in x['director']]) if len(x['director']) > 0 else ''
    
    # Give more weight to directors and cast by repeating them
    director = ' '.join([director] * 3)  # Repeat director 3 times for more weight
    cast = ' '.join([cast] * 2)  # Repeat cast 2 times for more weight
    
    return f"{genres} {keywords} {director} {cast} {x['overview']}"

movies_df['combined_features'] = movies_df.apply(create_soup, axis=1)

# --- 4. Vectorization & Similarity Calculation (for "Similar Movies") ---
tfidf = TfidfVectorizer(stop_words='english')
tfidf_matrix = tfidf.fit_transform(movies_df['combined_features'])
cosine_sim = linear_kernel(tfidf_matrix, tfidf_matrix)
indices = pd.Series(movies_df.index, index=movies_df['title']).drop_duplicates()

# --- 5. Recommendation and Listing Functions ---

def get_similar_movies(title, cosine_sim=cosine_sim):
    """
    This is the original function to find movies similar to a given one.
    """
    # Clean the user's input and perform a case-insensitive search for the movie title
    mask = indices.index.str.lower() == title.strip().lower()
    matching_titles = indices.index[mask]
    
    # If no match is found, return the "not found" message
    if len(matching_titles) == 0:
        return f"Movie '{title}' not found."
    
    # Use the first title that matched (to get the correct casing)
    actual_title = matching_titles[0]
    
    idx = indices[actual_title]
    sim_scores = sorted(list(enumerate(cosine_sim[idx])), key=lambda x: x[1], reverse=True)
    sim_scores = sim_scores[1:11]
    movie_indices = [i[0] for i in sim_scores]
    return movies_df['title'].iloc[movie_indices]

def get_movies_by_mood(mood, top_n=10):
    """
    Recommends movies based on the user's mood.
    
    Args:
        mood (str): The mood/emotion the user is looking for (e.g., 'happy', 'sad', 'exciting')
        top_n (int): Number of movies to recommend
        
    Returns:
        pandas.Series: A series of movie titles matching the mood
    """
    # Dictionary mapping moods to keywords
    mood_keywords = {
        'happy': ['comedy', 'family', 'animation', 'musical', 'romance'],
        'sad': ['drama', 'tragedy', 'war', 'romance'],
        'exciting': ['action', 'adventure', 'thriller', 'sci-fi', 'fantasy'],
        'scary': ['horror', 'thriller', 'mystery'],
        'thoughtful': ['drama', 'documentary', 'history', 'biography'],
        'relaxing': ['animation', 'family', 'comedy', 'fantasy']
    }
    
    # Default to 'exciting' if mood not found
    keywords = mood_keywords.get(mood.lower(), mood_keywords['exciting'])
    
    # Create a score for each movie based on genre match
    def score_by_mood(movie):
        score = 0
        for genre in movie['genres']:
            if any(keyword.lower() in genre.lower() for keyword in keywords):
                score += 1
        return score
    
    # Apply scoring and sort
    movies_df['mood_score'] = movies_df.apply(score_by_mood, axis=1)
    mood_matches = movies_df.sort_values(['mood_score', 'vote_average'], ascending=[False, False])
    
    # Return top matches
    return mood_matches['title'].head(top_n)

def get_top_rated_movies(top_n=10):
    """
    Calculates a weighted rating to find the top-rated movies.
    This prevents movies with a few high ratings from dominating.
    """
    C = movies_df['vote_average'].mean()
    m = movies_df['vote_count'].quantile(0.90) # Use 90th percentile as the minimum votes threshold

    qualified_movies = movies_df.copy().loc[movies_df['vote_count'] >= m]

    def weighted_rating(x, m=m, C=C):
        v = x['vote_count']
        R = x['vote_average']
        return (v / (v + m) * R) + (m / (m + v) * C)

    qualified_movies['score'] = qualified_movies.apply(weighted_rating, axis=1)
    qualified_movies = qualified_movies.sort_values('score', ascending=False)

    return qualified_movies['title'].head(top_n)

def get_popular_movies(top_n=10):
    """
    Returns the most popular movies based on the 'popularity' score.
    """
    popular_movies = movies_df.sort_values('popularity', ascending=False)
    return popular_movies['title'].head(top_n)

def get_latest_movies(top_n=10):
    """
    Returns the most recently released movies.
    """
    # Ensure release_date is a datetime object to sort correctly
    movies_df['release_date'] = pd.to_datetime(movies_df['release_date'], errors='coerce')
    latest_movies = movies_df.sort_values('release_date', ascending=False)
    return latest_movies['title'].head(top_n)


# --- 6. Interactive User Interface ---
def main():
    """
    Main function to run the interactive movie recommender.
    """
    while True:
        print("\n--- Enhanced Movie Recommendation System ---")
        print("1. Show Top 10 Rated Movies")
        print("2. Show Top 10 Popular Movies")
        print("3. Show 10 Latest Movies")
        print("4. Find Movies Similar to a Movie")
        print("5. Get Movies Based on Your Mood")
        print("6. Exit")

        choice = input("Enter your choice (1-6): ")

        if choice == '1':
            print("\n--- Top 10 Rated Movies ---")
            print(get_top_rated_movies())
        elif choice == '2':
            print("\n--- Top 10 Popular Movies ---")
            print(get_popular_movies())
        elif choice == '3':
            print("\n--- 10 Latest Movies ---")
            print(get_latest_movies())
        elif choice == '4':
            movie_title = input("Enter a movie title to find similar movies: ")
            print(f"\n--- Movies Similar to '{movie_title}' ---")
            print(get_similar_movies(movie_title))
        elif choice == '5':
            print("\n--- Mood-Based Recommendations ---")
            print("Available moods: happy, sad, exciting, scary, thoughtful, relaxing")
            mood = input("How are you feeling today? ")
            print(f"\n--- Movies for when you're feeling {mood} ---")
            print(get_movies_by_mood(mood))
        elif choice == '6':
            print("Thank you for using the enhanced recommender. Goodbye!")
            break
        else:
            print("Invalid choice. Please enter a number between 1 and 6.")

# Run the main function when the script is executed
if __name__ == '__main__':
    main()