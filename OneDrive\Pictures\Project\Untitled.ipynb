{"cells": [{"cell_type": "code", "execution_count": 8, "id": "1b5ace6b-df8e-4a43-be66-6cca44394e74", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data loaded successfully!\n"]}], "source": ["import pandas as pd\n", "import ast\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.neighbors import NearestNeighbors\n", "\n", "try:\n", "    movies_df = pd.read_csv('tmdb_5000_movies.csv')\n", "    credits_df = pd.read_csv('tmdb_5000_credits.csv')\n", "    \n", "    # Merge the dataframes into one\n", "    movies_df = movies_df.merge(credits_df, on='title')\n", "    \n", "    # You can display the first few rows to see what the data looks like\n", "    print(\"Data loaded successfully!\")\n", "    movies_df.head(2)\n", "    \n", "except FileNotFoundError:\n", "    print(\"Error: Make sure 'tmdb_5000_movies.csv' and 'tmdb_5000_credits.csv' are in the same folder as your notebook.\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "0e59e29d-3876-4b53-87e4-84e1f08fbdc1", "metadata": {}, "outputs": [], "source": ["def parse_features(feature_list_str):\n", "    try:\n", "        return [item['name'] for item in ast.literal_eval(feature_list_str)]\n", "    except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "        return []\n", "\n", "for feature in ['genres', 'keywords']:\n", "    movies_df[feature] = movies_df[feature].apply(parse_features)\n", "\n", "movies_df['overview'] = movies_df['overview'].fillna('')\n"]}, {"cell_type": "code", "execution_count": 10, "id": "80e7eb92-ef4f-486b-a8da-3294475948bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature engineering complete. 'combined_features' column created.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>combined_features</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Avatar</td>\n", "      <td>Action Adventure Fantasy ScienceFiction cultur...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pirates of the Caribbean: At World's End</td>\n", "      <td>Adventure Fantasy Action ocean drugabuse exoti...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      title  \\\n", "0                                    Avatar   \n", "1  Pirates of the Caribbean: At World's End   \n", "\n", "                                   combined_features  \n", "0  Action Adventure Fantasy ScienceFiction cultur...  \n", "1  Adventure Fantasy Action ocean drugabuse exoti...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def create_soup(x):\n", "    genres = ' '.join([g.replace(\" \", \"\") for g in x['genres']])\n", "    keywords = ' '.join([k.replace(\" \", \"\") for k in x['keywords']])\n", "    return f\"{genres} {keywords} {x['overview']}\"\n", "\n", "movies_df['combined_features'] = movies_df.apply(create_soup, axis=1)\n", "\n", "print(\"Feature engineering complete. 'combined_features' column created.\")\n", "# You can inspect a sample of the combined features\n", "movies_df[['title', 'combined_features']].head(2)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "897e8808-9952-47d7-8bf0-a73f9b805c00", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TF-IDF matrix created and KNN model is ready.\n"]}], "source": ["tfidf = TfidfVectorizer(stop_words='english')\n", "tfidf_matrix = tfidf.fit_transform(movies_df['combined_features'])\n", "\n", "# Create and fit the KNN model\n", "knn_model = NearestNeighbors(n_neighbors=11, algorithm='brute', metric='cosine')\n", "knn_model.fit(tfidf_matrix)\n", "\n", "# Create a mapping from movie titles to dataframe indices\n", "indices = pd.Series(movies_df.index, index=movies_df['title']).drop_duplicates()\n", "\n", "print(\"TF-IDF matrix created and KNN model is ready.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "639d2521-11fe-4a63-b409-71c9eb0a7df9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All recommendation functions are defined.\n"]}], "source": ["def get_similar_movies(title):\n", "    # Clean the user's input and perform a case-insensitive search for the movie title\n", "    mask = indices.index.str.lower() == title.strip().lower()\n", "    matching_titles = indices.index[mask]\n", "    \n", "    # If no match is found, return the \"not found\" message\n", "    if len(matching_titles) == 0:\n", "        return f\"Movie '{title}' not found.\"\n", "    \n", "    # Use the first title that matched (to get the correct casing)\n", "    actual_title = matching_titles[0]\n", "    \n", "    # Now proceed with the original logic using the correctly cased title\n", "    idx = indices[actual_title]\n", "    movie_vector = tfidf_matrix[idx]\n", "    distances, movie_indices = knn_model.kneighbors(movie_vector)\n", "    similar_movies_indices = movie_indices.flatten()[1:]\n", "    return movies_df['title'].iloc[similar_movies_indices]\n", "\n", "def get_top_rated_movies(top_n=10):\n", "    C = movies_df['vote_average'].mean()\n", "    m = movies_df['vote_count'].quantile(0.90)\n", "    qualified_movies = movies_df.copy().loc[movies_df['vote_count'] >= m]\n", "    def weighted_rating(x, m=m, C=C):\n", "        v = x['vote_count']\n", "        R = x['vote_average']\n", "        return (v / (v + m) * R) + (m / (m + v) * C)\n", "    qualified_movies['score'] = qualified_movies.apply(weighted_rating, axis=1)\n", "    qualified_movies = qualified_movies.sort_values('score', ascending=False)\n", "    return qualified_movies['title'].head(top_n)\n", "\n", "def get_popular_movies(top_n=10):\n", "    popular_movies = movies_df.sort_values('popularity', ascending=False)\n", "    return popular_movies['title'].head(top_n)\n", "\n", "def get_latest_movies(top_n=10):\n", "    movies_df['release_date'] = pd.to_datetime(movies_df['release_date'], errors='coerce')\n", "    latest_movies = movies_df.sort_values('release_date', ascending=False)\n", "    return latest_movies['title'].head(top_n)\n", "\n", "print(\"All recommendation functions are defined.\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b26f6b2-3cdd-4481-b08f-760bea1f1a7f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Movie Recommendation System (KNN Version) ---\n", "1. Show Top 10 Rated Movies\n", "2. Show Top 10 Popular Movies\n", "3. Show 10 Latest Movies\n", "4. Find Movies Similar to a Movie\n", "5. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice (1-5):  4\n", "Enter a movie title to find similar movies:  fight club\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Movies Similar to 'fight club' ---\n", "2622            Promised Land\n", "2077          Never Back Down\n", "3638            The Machinist\n", "3622                      UHF\n", "2058     Before I Go to Sleep\n", "4766    This <PERSON>\n", "2641             District B13\n", "4049               Go for It!\n", "4050    <PERSON>, Texas Pop. 81\n", "3636       Everything Must Go\n", "Name: title, dtype: object\n", "\n", "--- Movie Recommendation System (KNN Version) ---\n", "1. Show Top 10 Rated Movies\n", "2. Show Top 10 Popular Movies\n", "3. Show 10 Latest Movies\n", "4. Find Movies Similar to a Movie\n", "5. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice (1-5):  1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Top 10 Rated Movies ---\n", "1883                         The Shawshank Redemption\n", "662                                        Fight Club\n", "65                                    The Dark Knight\n", "3235                                     Pulp Fiction\n", "96                                          Inception\n", "3340                                    The Godfather\n", "95                                       Interstellar\n", "809                                      <PERSON>\n", "329     The Lord of the Rings: The Return of the King\n", "1992                          The Empire Strikes Back\n", "Name: title, dtype: object\n", "\n", "--- Movie Recommendation System (KNN Version) ---\n", "1. Show Top 10 Rated Movies\n", "2. Show Top 10 Popular Movies\n", "3. Show 10 Latest Movies\n", "4. Find Movies Similar to a Movie\n", "5. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice (1-5):  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Top 10 Popular Movies ---\n", "546                                              Minions\n", "95                                          Interstellar\n", "788                                             Deadpool\n", "94                               Guardians of the Galaxy\n", "127                                   Mad Max: Fury Road\n", "28                                        Jurassic World\n", "199    Pirates of the Caribbean: The Curse of the Bla...\n", "82                        Dawn of the Planet of the Apes\n", "200                The Hunger Games: Mockingjay - Part 1\n", "88                                            Big Hero 6\n", "Name: title, dtype: object\n", "\n", "--- Movie Recommendation System (KNN Version) ---\n", "1. Show Top 10 Rated Movies\n", "2. Show Top 10 Popular Movies\n", "3. Show 10 Latest Movies\n", "4. Find Movies Similar to a Movie\n", "5. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice (1-5):  3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- 10 Latest Movies ---\n", "4260         Growing Up Smith\n", "3411    Two Lovers and a Bear\n", "3305               Mr. <PERSON>\n", "4726    The Birth of a Nation\n", "3252                    Kicks\n", "4041                Antibirth\n", "2275           Hands of Stone\n", "357                   <PERSON><PERSON><PERSON><PERSON>\n", "3016            <PERSON>'s Dragon\n", "72              Suicide Squad\n", "Name: title, dtype: object\n", "\n", "--- Movie Recommendation System (KNN Version) ---\n", "1. Show Top 10 Rated Movies\n", "2. Show Top 10 Popular Movies\n", "3. Show 10 Latest Movies\n", "4. Find Movies Similar to a Movie\n", "5. Exit\n"]}], "source": ["def main():\n", "    while True:\n", "        print(\"\\n--- Movie Recommendation System (KNN Version) ---\")\n", "        print(\"1. Show Top 10 Rated Movies\")\n", "        print(\"2. Show Top 10 Popular Movies\")\n", "        print(\"3. Show 10 Latest Movies\")\n", "        print(\"4. Find Movies Similar to a Movie\")\n", "        print(\"5. Exit\")\n", "        \n", "        choice = input(\"Enter your choice (1-5): \")\n", "        \n", "        if choice == '1':\n", "            print(\"\\n--- Top 10 Rated Movies ---\")\n", "            print(get_top_rated_movies())\n", "        elif choice == '2':\n", "            print(\"\\n--- Top 10 Popular Movies ---\")\n", "            print(get_popular_movies())\n", "        elif choice == '3':\n", "            print(\"\\n--- 10 Latest Movies ---\")\n", "            print(get_latest_movies())\n", "        elif choice == '4':\n", "            movie_title = input(\"Enter a movie title to find similar movies: \")\n", "            print(f\"\\n--- Movies Similar to '{movie_title}' ---\")\n", "            print(get_similar_movies(movie_title))\n", "        elif choice == '5':\n", "            print(\"To stop the program, interrupt the kernel.\")\n", "            break\n", "        else:\n", "            print(\"Invalid choice. Please enter a number between 1 and 5.\")\n", "\n", "# Start the program\n", "main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b4046e86-2628-41e4-8faf-46f26c7437d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}