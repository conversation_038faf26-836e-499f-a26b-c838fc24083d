import pandas as pd
import ast
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import linear_kernel
from sklearn.neighbors import NearestNeighbors
from user_profiles import UserProfile, UserProfileManager

class MovieRecommender:
    """Enhanced movie recommender with user profiles support"""
    
    def __init__(self):
        """Initialize the movie recommender"""
        # Load data
        self.load_data()
        
        # Initialize profile manager
        self.profile_manager = UserProfileManager()
        self.active_profile = None
    
    def load_data(self):
        """Load and prepare movie data"""
        try:
            # Load data
            movies_df = pd.read_csv('tmdb_5000_movies.csv')
            credits_df = pd.read_csv('tmdb_5000_credits.csv')
            self.movies_df = movies_df.merge(credits_df, on='title')
            
            # Preprocess data
            self.preprocess_data()
            
            return True
        except FileNotFoundError:
            print("Error: Movie data files not found.")
            return False
    
    def preprocess_data(self):
        """Preprocess movie data"""
        # Parse features
        def parse_features(feature_list_str):
            try:
                return [item['name'] for item in ast.literal_eval(feature_list_str)]
            except (ValueError, SyntaxError):
                return []
        
        # Parse cast
        def parse_cast(cast_str, limit=3):
            try:
                cast_list = ast.literal_eval(cast_str)
                return [item['name'] for item in cast_list[:limit]]
            except (ValueError, SyntaxError):
                return []
        
        # Parse director
        def parse_director(crew_str):
            try:
                crew_list = ast.literal_eval(crew_str)
                directors = [item['name'] for item in crew_list if item['job'] == 'Director']
                return directors if directors else []
            except (ValueError, SyntaxError):
                return []
        
        # Apply parsing functions
        for feature in ['genres', 'keywords']:
            self.movies_df[feature] = self.movies_df[feature].apply(parse_features)
        
        self.movies_df['cast_names'] = self.movies_df['cast'].apply(parse_cast)
        self.movies_df['director'] = self.movies_df['crew'].apply(parse_director)
        self.movies_df['overview'] = self.movies_df['overview'].fillna('')
        
        # Create soup for content-based filtering
        def create_soup(x):
            genres = ' '.join([g.replace(" ", "") for g in x['genres']])
            keywords = ' '.join([k.replace(" ", "") for k in x['keywords']])
            cast = ' '.join([c.replace(" ", "") for c in x['cast_names']]) if len(x['cast_names']) > 0 else ''
            director = ' '.join([d.replace(" ", "") for d in x['director']]) if len(x['director']) > 0 else ''
            
            # Give more weight to directors and cast
            director = ' '.join([director] * 3)
            cast = ' '.join([cast] * 2)
            
            return f"{genres} {keywords} {director} {cast} {x['overview']}"
        
        self.movies_df['combined_features'] = self.movies_df.apply(create_soup, axis=1)
        
        # Create TF-IDF matrix
        tfidf = TfidfVectorizer(stop_words='english')
        self.tfidf_matrix = tfidf.fit_transform(self.movies_df['combined_features'])
        self.cosine_sim = linear_kernel(self.tfidf_matrix, self.tfidf_matrix)
        self.indices = pd.Series(self.movies_df.index, index=self.movies_df['title']).drop_duplicates()
    
    def create_user_profile(self, username):
        """Create a new user profile"""
        return self.profile_manager.create_profile(username)
    
    def load_user_profile(self, username):
        """Load an existing user profile"""
        success = self.profile_manager.load_profile(username)
        if success:
            self.active_profile = self.profile_manager.get_active_profile()
        return success
    
    def get_all_profiles(self):
        """Get list of all profile usernames"""
        return self.profile_manager.get_all_profiles()
    
    def get_active_profile(self):
        """Get the active user profile"""
        return self.profile_manager.get_active_profile()
    
    def add_watched_movie(self, title):
        """Add a movie to the user's watched list"""
        if not self.active_profile:
            return False
        
        # Find movie in dataframe
        movie_data = self.movies_df[self.movies_df['title'] == title]
        if movie_data.empty:
            return False
        
        # Extract movie data
        movie_info = movie_data.iloc[0].to_dict()
        
        # Add to watched movies
        return self.active_profile.add_watched_movie(movie_info)
    
    def rate_movie(self, title, rating):
        """Rate a movie (1-5 stars)"""
        if not self.active_profile:
            return False
        
        # Find movie in dataframe
        movie_data = self.movies_df[self.movies_df['title'] == title]
        if movie_data.empty:
            return False
        
        # Extract movie ID
        movie_id = movie_data.iloc[0]['id']
        
        # Rate movie
        return self.active_profile.rate_movie(movie_id, title, rating)
    
    def get_similar_movies(self, title, top_n=10):
        """Find similar movies based on content"""
        # Clean the user's input and perform a case-insensitive search
        mask = self.indices.index.str.lower() == title.lower().strip()
        matching_titles = self.indices.index[mask]
        
        # If no match is found
        if len(matching_titles) == 0:
            return f"Movie '{title}' not found in the database."
        
        # Use the first title that matched (to get the correct casing)
        actual_title = matching_titles[0]
        
        # Get similar movies
        idx = self.indices[actual_title]
        sim_scores = sorted(list(enumerate(self.cosine_sim[idx])), key=lambda x: x[1], reverse=True)
        sim_scores = sim_scores[1:top_n+1]  # top N similar movies
        movie_indices = [i[0] for i in sim_scores]
        
        # If user has an active profile, add this movie to watched
        if self.active_profile:
            self.add_watched_movie(actual_title)
        
        return self.movies_df.iloc[movie_indices]
    
    def get_top_rated_movies(self, top_n=10):
        """Get top rated movies using weighted rating"""
        C = self.movies_df['vote_average'].mean()
        m = self.movies_df['vote_count'].quantile(0.90)
        
        qualified_movies = self.movies_df.copy().loc[self.movies_df['vote_count'] >= m]
        
        def weighted_rating(x, m=m, C=C):
            v = x['vote_count']
            R = x['vote_average']
            return (v / (v + m) * R) + (m / (m + v) * C)
        
        qualified_movies['score'] = qualified_movies.apply(weighted_rating, axis=1)
        qualified_movies = qualified_movies.sort_values('score', ascending=False)
        
        return qualified_movies.head(top_n)
    
    def get_popular_movies(self, top_n=10):
        """Get popular movies based on popularity score"""
        popular_movies = self.movies_df.sort_values('popularity', ascending=False)
        return popular_movies.head(top_n)
    
    def get_latest_movies(self, top_n=10):
        """Get latest movies based on release date"""
        # Ensure release_date is a datetime object
        self.movies_df['release_date'] = pd.to_datetime(self.movies_df['release_date'], errors='coerce')
        latest_movies = self.movies_df.sort_values('release_date', ascending=False)
        return latest_movies.head(top_n)
    
    def get_movies_by_mood(self, mood, top_n=10):
        """Get movies based on mood"""
        # Dictionary mapping moods to keywords
        mood_keywords = {
            'happy': ['comedy', 'family', 'animation', 'musical', 'romance'],
            'sad': ['drama', 'tragedy', 'war', 'romance'],
            'exciting': ['action', 'adventure', 'thriller', 'sci-fi', 'fantasy'],
            'scary': ['horror', 'thriller', 'mystery'],
            'thoughtful': ['drama', 'documentary', 'history', 'biography'],
            'relaxing': ['animation', 'family', 'comedy', 'fantasy']
        }
        
        # Default to 'exciting' if mood not found
        keywords = mood_keywords.get(mood.lower(), mood_keywords['exciting'])
        
        # Create a score for each movie based on genre match
        def score_by_mood(movie):
            score = 0
            for genre in movie['genres']:
                if any(keyword.lower() in genre.lower() for keyword in keywords):
                    score += 1
            return score
        
        # Apply scoring and sort
        self.movies_df['mood_score'] = self.movies_df.apply(score_by_mood, axis=1)
        mood_matches = self.movies_df.sort_values(['mood_score', 'vote_average'], ascending=[False, False])
        
        # Return top matches
        return mood_matches.head(top_n)
    
    def get_personalized_recommendations(self, top_n=10):
        """Get personalized recommendations based on user profile"""
        if not self.active_profile:
            # If no active profile, return popular movies
            return self.get_popular_movies(top_n)
        
        # Get personalized recommendations
        return self.active_profile.get_personalized_recommendations(self.movies_df, top_n)


# Example usage
if __name__ == "__main__":
    # Create recommender
    recommender = MovieRecommender()
    
    # Create a new profile
    recommender.create_user_profile("test_user")
    
    # Get similar movies
    similar_movies = recommender.get_similar_movies("Avatar")
    print("Similar movies to Avatar:")
    print(similar_movies['title'].tolist())
    
    # Rate a movie
    recommender.rate_movie("Avatar", 4)
    
    # Get personalized recommendations
    personalized = recommender.get_personalized_recommendations()
    print("\nPersonalized recommendations:")
    print(personalized['title'].tolist())