# Enhanced Movie Recommendation System

A comprehensive movie recommendation system that uses machine learning techniques to provide personalized movie recommendations based on content similarity, user preferences, and mood.

## Features

- **Content-Based Recommendations**: Find movies similar to ones you like based on genres, keywords, plot, cast, and directors
- **User Profiles**: Create and manage user profiles to track viewing history and preferences
- **Personalized Recommendations**: Get recommendations tailored to your viewing history and preferences
- **Mood-Based Recommendations**: Find movies that match your current mood (happy, sad, exciting, etc.)
- **Visualization Tools**: Visualize movie relationships and genre distributions
- **Multiple Interfaces**: Use either command-line or graphical user interface

## Components

- `recommender.py`: Original command-line movie recommendation system
- `recommender_gui.py`: Graphical user interface for the recommendation system
- `recommender_with_profiles.py`: Enhanced recommendation system with user profiles support
- `user_profiles.py`: User profile management system
- `movie_visualizer.py`: Visualization tools for movie data

## Installation

1. Clone this repository
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Ensure you have the TMDB dataset files in the same directory:
   - `tmdb_5000_movies.csv`
   - `tmdb_5000_credits.csv`

## Usage

### Command-Line Interface

Run the original recommender:
```
python recommender.py
```

### Graphical User Interface

Run the GUI version:
```
python recommender_gui.py
```

### With User Profiles

```python
from recommender_with_profiles import MovieRecommender

# Create recommender
recommender = MovieRecommender()

# Create a new profile
recommender.create_user_profile("your_username")

# Get similar movies
similar_movies = recommender.get_similar_movies("Avatar")
print(similar_movies['title'].tolist())

# Rate a movie
recommender.rate_movie("Avatar", 4)

# Get personalized recommendations
personalized = recommender.get_personalized_recommendations()
print(personalized['title'].tolist())
```

### Visualization

```python
from movie_visualizer import MovieVisualizer
import pandas as pd

# Load data
movies_df = pd.read_csv('tmdb_5000_movies.csv')
credits_df = pd.read_csv('tmdb_5000_credits.csv')
movies_df = movies_df.merge(credits_df, on='title')

# Create visualizer
visualizer = MovieVisualizer(movies_df)

# Plot genre distribution
visualizer.plot_genre_distribution()

# Plot rating vs popularity
visualizer.plot_rating_vs_popularity()
```

## Data

This system uses the TMDB 5000 Movie Dataset, which includes:
- Movie metadata (title, genres, keywords, overview, etc.)
- Cast and crew information
- Popularity and rating metrics

## Future Improvements

- Web interface using Flask or Streamlit
- Integration with external APIs for real-time movie data
- Collaborative filtering for even better recommendations
- More advanced visualization tools
- Mobile app version

## License

This project is open source and available under the MIT License.