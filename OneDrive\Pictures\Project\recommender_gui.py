import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pandas as pd
import ast
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import linear_kernel
from sklearn.neighbors import NearestNeighbors

class MovieRecommenderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Movie Recommendation System")
        self.root.geometry("800x600")
        self.root.configure(bg="#f0f0f0")
        
        # Load data and prepare recommendation system
        self.load_data()
        
        # Create GUI elements
        self.create_widgets()
    
    def load_data(self):
        """Load and prepare movie data for recommendations"""
        try:
            # Show loading message
            loading_label = tk.Label(self.root, text="Loading movie data...", font=("Arial", 14), bg="#f0f0f0")
            loading_label.pack(pady=20)
            self.root.update()
            
            # Load data
            movies_df = pd.read_csv('tmdb_5000_movies.csv')
            credits_df = pd.read_csv('tmdb_5000_credits.csv')
            self.movies_df = movies_df.merge(credits_df, on='title')
            
            # Data preprocessing
            self.preprocess_data()
            
            # Remove loading message
            loading_label.destroy()
            
        except FileNotFoundError:
            messagebox.showerror("Error", "Movie data files not found. Make sure 'tmdb_5000_movies.csv' and 'tmdb_5000_credits.csv' are in the same folder.")
            self.root.destroy()
    
    def preprocess_data(self):
        """Preprocess movie data for recommendations"""
        # Parse features
        def parse_features(feature_list_str):
            try:
                return [item['name'] for item in ast.literal_eval(feature_list_str)]
            except (ValueError, SyntaxError):
                return []
        
        # Parse cast
        def parse_cast(cast_str, limit=3):
            try:
                cast_list = ast.literal_eval(cast_str)
                return [item['name'] for item in cast_list[:limit]]
            except (ValueError, SyntaxError):
                return []
        
        # Parse director
        def parse_director(crew_str):
            try:
                crew_list = ast.literal_eval(crew_str)
                directors = [item['name'] for item in crew_list if item['job'] == 'Director']
                return directors if directors else []
            except (ValueError, SyntaxError):
                return []
        
        # Apply parsing functions
        for feature in ['genres', 'keywords']:
            self.movies_df[feature] = self.movies_df[feature].apply(parse_features)
        
        self.movies_df['cast_names'] = self.movies_df['cast'].apply(parse_cast)
        self.movies_df['director'] = self.movies_df['crew'].apply(parse_director)
        self.movies_df['overview'] = self.movies_df['overview'].fillna('')
        
        # Create soup for content-based filtering
        def create_soup(x):
            genres = ' '.join([g.replace(" ", "") for g in x['genres']])
            keywords = ' '.join([k.replace(" ", "") for k in x['keywords']])
            cast = ' '.join([c.replace(" ", "") for c in x['cast_names']]) if len(x['cast_names']) > 0 else ''
            director = ' '.join([d.replace(" ", "") for d in x['director']]) if len(x['director']) > 0 else ''
            
            # Give more weight to directors and cast
            director = ' '.join([director] * 3)
            cast = ' '.join([cast] * 2)
            
            return f"{genres} {keywords} {director} {cast} {x['overview']}"
        
        self.movies_df['combined_features'] = self.movies_df.apply(create_soup, axis=1)
        
        # Create TF-IDF matrix
        tfidf = TfidfVectorizer(stop_words='english')
        self.tfidf_matrix = tfidf.fit_transform(self.movies_df['combined_features'])
        self.cosine_sim = linear_kernel(self.tfidf_matrix, self.tfidf_matrix)
        self.indices = pd.Series(self.movies_df.index, index=self.movies_df['title']).drop_duplicates()
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Create notebook (tabbed interface)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.similar_tab = ttk.Frame(self.notebook)
        self.top_rated_tab = ttk.Frame(self.notebook)
        self.popular_tab = ttk.Frame(self.notebook)
        self.latest_tab = ttk.Frame(self.notebook)
        self.mood_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.similar_tab, text="Similar Movies")
        self.notebook.add(self.top_rated_tab, text="Top Rated")
        self.notebook.add(self.popular_tab, text="Popular")
        self.notebook.add(self.latest_tab, text="Latest")
        self.notebook.add(self.mood_tab, text="Mood Based")
        
        # Setup each tab
        self.setup_similar_tab()
        self.setup_top_rated_tab()
        self.setup_popular_tab()
        self.setup_latest_tab()
        self.setup_mood_tab()
    
    def setup_similar_tab(self):
        """Setup the Similar Movies tab"""
        # Title
        title_label = tk.Label(self.similar_tab, text="Find Similar Movies", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = tk.Label(self.similar_tab, text="Enter a movie title to find similar movies based on content")
        desc_label.pack(pady=5)
        
        # Input frame
        input_frame = ttk.Frame(self.similar_tab)
        input_frame.pack(pady=10, fill='x', padx=20)
        
        # Movie title entry
        title_label = tk.Label(input_frame, text="Movie Title:")
        title_label.pack(side='left', padx=5)
        
        self.title_entry = ttk.Entry(input_frame, width=40)
        self.title_entry.pack(side='left', padx=5)
        
        # Search button
        search_button = ttk.Button(input_frame, text="Find Similar", command=self.find_similar_movies)
        search_button.pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.similar_tab, text="Recommendations")
        results_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.similar_results = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.similar_results.pack(padx=10, pady=10, fill='both', expand=True)
    
    def setup_top_rated_tab(self):
        """Setup the Top Rated tab"""
        # Title
        title_label = tk.Label(self.top_rated_tab, text="Top Rated Movies", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = tk.Label(self.top_rated_tab, text="Movies with the highest weighted rating scores")
        desc_label.pack(pady=5)
        
        # Number selector
        num_frame = ttk.Frame(self.top_rated_tab)
        num_frame.pack(pady=10)
        
        num_label = tk.Label(num_frame, text="Number of movies:")
        num_label.pack(side='left', padx=5)
        
        self.top_rated_num = ttk.Spinbox(num_frame, from_=5, to=50, width=5)
        self.top_rated_num.set(10)
        self.top_rated_num.pack(side='left', padx=5)
        
        show_button = ttk.Button(num_frame, text="Show Movies", command=self.show_top_rated)
        show_button.pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.top_rated_tab, text="Top Rated Movies")
        results_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.top_rated_results = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.top_rated_results.pack(padx=10, pady=10, fill='both', expand=True)
    
    def setup_popular_tab(self):
        """Setup the Popular tab"""
        # Title
        title_label = tk.Label(self.popular_tab, text="Popular Movies", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = tk.Label(self.popular_tab, text="Movies with the highest popularity scores")
        desc_label.pack(pady=5)
        
        # Number selector
        num_frame = ttk.Frame(self.popular_tab)
        num_frame.pack(pady=10)
        
        num_label = tk.Label(num_frame, text="Number of movies:")
        num_label.pack(side='left', padx=5)
        
        self.popular_num = ttk.Spinbox(num_frame, from_=5, to=50, width=5)
        self.popular_num.set(10)
        self.popular_num.pack(side='left', padx=5)
        
        show_button = ttk.Button(num_frame, text="Show Movies", command=self.show_popular)
        show_button.pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.popular_tab, text="Popular Movies")
        results_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.popular_results = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.popular_results.pack(padx=10, pady=10, fill='both', expand=True)
    
    def setup_latest_tab(self):
        """Setup the Latest tab"""
        # Title
        title_label = tk.Label(self.latest_tab, text="Latest Movies", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = tk.Label(self.latest_tab, text="Most recently released movies")
        desc_label.pack(pady=5)
        
        # Number selector
        num_frame = ttk.Frame(self.latest_tab)
        num_frame.pack(pady=10)
        
        num_label = tk.Label(num_frame, text="Number of movies:")
        num_label.pack(side='left', padx=5)
        
        self.latest_num = ttk.Spinbox(num_frame, from_=5, to=50, width=5)
        self.latest_num.set(10)
        self.latest_num.pack(side='left', padx=5)
        
        show_button = ttk.Button(num_frame, text="Show Movies", command=self.show_latest)
        show_button.pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.latest_tab, text="Latest Movies")
        results_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.latest_results = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.latest_results.pack(padx=10, pady=10, fill='both', expand=True)
    
    def setup_mood_tab(self):
        """Setup the Mood Based tab"""
        # Title
        title_label = tk.Label(self.mood_tab, text="Mood-Based Recommendations", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = tk.Label(self.mood_tab, text="Get movie recommendations based on your mood")
        desc_label.pack(pady=5)
        
        # Mood selector
        mood_frame = ttk.Frame(self.mood_tab)
        mood_frame.pack(pady=10)
        
        mood_label = tk.Label(mood_frame, text="Select your mood:")
        mood_label.pack(side='left', padx=5)
        
        self.mood_var = tk.StringVar()
        moods = ["happy", "sad", "exciting", "scary", "thoughtful", "relaxing"]
        self.mood_combo = ttk.Combobox(mood_frame, textvariable=self.mood_var, values=moods, width=15)
        self.mood_combo.current(0)
        self.mood_combo.pack(side='left', padx=5)
        
        # Number selector
        num_label = tk.Label(mood_frame, text="Number of movies:")
        num_label.pack(side='left', padx=5)
        
        self.mood_num = ttk.Spinbox(mood_frame, from_=5, to=50, width=5)
        self.mood_num.set(10)
        self.mood_num.pack(side='left', padx=5)
        
        show_button = ttk.Button(mood_frame, text="Show Movies", command=self.show_mood_based)
        show_button.pack(side='left', padx=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.mood_tab, text="Mood-Based Recommendations")
        results_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.mood_results = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.mood_results.pack(padx=10, pady=10, fill='both', expand=True)
    
    def find_similar_movies(self):
        """Find similar movies based on user input"""
        title = self.title_entry.get().strip()
        if not title:
            messagebox.showwarning("Input Error", "Please enter a movie title")
            return
        
        # Clear previous results
        self.similar_results.delete(1.0, tk.END)
        
        # Clean the user's input and perform a case-insensitive search
        mask = self.indices.index.str.lower() == title.lower()
        matching_titles = self.indices.index[mask]
        
        # If no match is found
        if len(matching_titles) == 0:
            self.similar_results.insert(tk.END, f"Movie '{title}' not found in the database.\n")
            return
        
        # Use the first title that matched (to get the correct casing)
        actual_title = matching_titles[0]
        
        # Get similar movies
        idx = self.indices[actual_title]
        sim_scores = sorted(list(enumerate(self.cosine_sim[idx])), key=lambda x: x[1], reverse=True)
        sim_scores = sim_scores[1:11]  # top 10 similar movies
        movie_indices = [i[0] for i in sim_scores]
        similar_movies = self.movies_df['title'].iloc[movie_indices]
        
        # Display results with additional info
        self.similar_results.insert(tk.END, f"Movies similar to '{actual_title}':\n\n")
        
        for i, movie in enumerate(similar_movies, 1):
            movie_data = self.movies_df[self.movies_df['title'] == movie].iloc[0]
            genres = ', '.join(movie_data['genres'][:3]) if len(movie_data['genres']) > 0 else 'N/A'
            directors = ', '.join(movie_data['director']) if len(movie_data['director']) > 0 else 'N/A'
            cast = ', '.join(movie_data['cast_names']) if len(movie_data['cast_names']) > 0 else 'N/A'
            
            self.similar_results.insert(tk.END, f"{i}. {movie}\n")
            self.similar_results.insert(tk.END, f"   Genres: {genres}\n")
            self.similar_results.insert(tk.END, f"   Director: {directors}\n")
            self.similar_results.insert(tk.END, f"   Cast: {cast}\n\n")
    
    def get_top_rated_movies(self, top_n=10):
        """Get top rated movies using weighted rating"""
        C = self.movies_df['vote_average'].mean()
        m = self.movies_df['vote_count'].quantile(0.90)
        
        qualified_movies = self.movies_df.copy().loc[self.movies_df['vote_count'] >= m]
        
        def weighted_rating(x, m=m, C=C):
            v = x['vote_count']
            R = x['vote_average']
            return (v / (v + m) * R) + (m / (m + v) * C)
        
        qualified_movies['score'] = qualified_movies.apply(weighted_rating, axis=1)
        qualified_movies = qualified_movies.sort_values('score', ascending=False)
        
        return qualified_movies[['title', 'genres', 'director', 'cast_names', 'vote_average', 'vote_count']].head(top_n)
    
    def show_top_rated(self):
        """Display top rated movies"""
        try:
            top_n = int(self.top_rated_num.get())
        except ValueError:
            top_n = 10
        
        # Clear previous results
        self.top_rated_results.delete(1.0, tk.END)
        
        # Get top rated movies
        top_movies = self.get_top_rated_movies(top_n)
        
        # Display results
        self.top_rated_results.insert(tk.END, f"Top {top_n} Rated Movies:\n\n")
        
        for i, (_, movie) in enumerate(top_movies.iterrows(), 1):
            genres = ', '.join(movie['genres'][:3]) if len(movie['genres']) > 0 else 'N/A'
            directors = ', '.join(movie['director']) if len(movie['director']) > 0 else 'N/A'
            cast = ', '.join(movie['cast_names']) if len(movie['cast_names']) > 0 else 'N/A'
            
            self.top_rated_results.insert(tk.END, f"{i}. {movie['title']}\n")
            self.top_rated_results.insert(tk.END, f"   Rating: {movie['vote_average']} (from {movie['vote_count']} votes)\n")
            self.top_rated_results.insert(tk.END, f"   Genres: {genres}\n")
            self.top_rated_results.insert(tk.END, f"   Director: {directors}\n")
            self.top_rated_results.insert(tk.END, f"   Cast: {cast}\n\n")
    
    def get_popular_movies(self, top_n=10):
        """Get popular movies based on popularity score"""
        popular_movies = self.movies_df.sort_values('popularity', ascending=False)
        return popular_movies[['title', 'genres', 'director', 'cast_names', 'popularity']].head(top_n)
    
    def show_popular(self):
        """Display popular movies"""
        try:
            top_n = int(self.popular_num.get())
        except ValueError:
            top_n = 10
        
        # Clear previous results
        self.popular_results.delete(1.0, tk.END)
        
        # Get popular movies
        popular_movies = self.get_popular_movies(top_n)
        
        # Display results
        self.popular_results.insert(tk.END, f"Top {top_n} Popular Movies:\n\n")
        
        for i, (_, movie) in enumerate(popular_movies.iterrows(), 1):
            genres = ', '.join(movie['genres'][:3]) if len(movie['genres']) > 0 else 'N/A'
            directors = ', '.join(movie['director']) if len(movie['director']) > 0 else 'N/A'
            cast = ', '.join(movie['cast_names']) if len(movie['cast_names']) > 0 else 'N/A'
            
            self.popular_results.insert(tk.END, f"{i}. {movie['title']}\n")
            self.popular_results.insert(tk.END, f"   Popularity Score: {movie['popularity']:.2f}\n")
            self.popular_results.insert(tk.END, f"   Genres: {genres}\n")
            self.popular_results.insert(tk.END, f"   Director: {directors}\n")
            self.popular_results.insert(tk.END, f"   Cast: {cast}\n\n")
    
    def get_latest_movies(self, top_n=10):
        """Get latest movies based on release date"""
        # Ensure release_date is a datetime object
        self.movies_df['release_date'] = pd.to_datetime(self.movies_df['release_date'], errors='coerce')
        latest_movies = self.movies_df.sort_values('release_date', ascending=False)
        return latest_movies[['title', 'genres', 'director', 'cast_names', 'release_date']].head(top_n)
    
    def show_latest(self):
        """Display latest movies"""
        try:
            top_n = int(self.latest_num.get())
        except ValueError:
            top_n = 10
        
        # Clear previous results
        self.latest_results.delete(1.0, tk.END)
        
        # Get latest movies
        latest_movies = self.get_latest_movies(top_n)
        
        # Display results
        self.latest_results.insert(tk.END, f"Top {top_n} Latest Movies:\n\n")
        
        for i, (_, movie) in enumerate(latest_movies.iterrows(), 1):
            genres = ', '.join(movie['genres'][:3]) if len(movie['genres']) > 0 else 'N/A'
            directors = ', '.join(movie['director']) if len(movie['director']) > 0 else 'N/A'
            cast = ', '.join(movie['cast_names']) if len(movie['cast_names']) > 0 else 'N/A'
            release_date = movie['release_date'].strftime('%Y-%m-%d') if pd.notna(movie['release_date']) else 'N/A'
            
            self.latest_results.insert(tk.END, f"{i}. {movie['title']}\n")
            self.latest_results.insert(tk.END, f"   Release Date: {release_date}\n")
            self.latest_results.insert(tk.END, f"   Genres: {genres}\n")
            self.latest_results.insert(tk.END, f"   Director: {directors}\n")
            self.latest_results.insert(tk.END, f"   Cast: {cast}\n\n")
    
    def get_movies_by_mood(self, mood, top_n=10):
        """Get movies based on mood"""
        # Dictionary mapping moods to keywords
        mood_keywords = {
            'happy': ['comedy', 'family', 'animation', 'musical', 'romance'],
            'sad': ['drama', 'tragedy', 'war', 'romance'],
            'exciting': ['action', 'adventure', 'thriller', 'sci-fi', 'fantasy'],
            'scary': ['horror', 'thriller', 'mystery'],
            'thoughtful': ['drama', 'documentary', 'history', 'biography'],
            'relaxing': ['animation', 'family', 'comedy', 'fantasy']
        }
        
        # Default to 'exciting' if mood not found
        keywords = mood_keywords.get(mood.lower(), mood_keywords['exciting'])
        
        # Create a score for each movie based on genre match
        def score_by_mood(movie):
            score = 0
            for genre in movie['genres']:
                if any(keyword.lower() in genre.lower() for keyword in keywords):
                    score += 1
            return score
        
        # Apply scoring and sort
        self.movies_df['mood_score'] = self.movies_df.apply(score_by_mood, axis=1)
        mood_matches = self.movies_df.sort_values(['mood_score', 'vote_average'], ascending=[False, False])
        
        # Return top matches
        return mood_matches[['title', 'genres', 'director', 'cast_names', 'mood_score', 'vote_average']].head(top_n)
    
    def show_mood_based(self):
        """Display mood-based movie recommendations"""
        mood = self.mood_var.get()
        try:
            top_n = int(self.mood_num.get())
        except ValueError:
            top_n = 10
        
        # Clear previous results
        self.mood_results.delete(1.0, tk.END)
        
        # Get mood-based recommendations
        mood_movies = self.get_movies_by_mood(mood, top_n)
        
        # Display results
        self.mood_results.insert(tk.END, f"Top {top_n} Movies for '{mood}' mood:\n\n")
        
        for i, (_, movie) in enumerate(mood_movies.iterrows(), 1):
            genres = ', '.join(movie['genres'][:3]) if len(movie['genres']) > 0 else 'N/A'
            directors = ', '.join(movie['director']) if len(movie['director']) > 0 else 'N/A'
            cast = ', '.join(movie['cast_names']) if len(movie['cast_names']) > 0 else 'N/A'
            
            self.mood_results.insert(tk.END, f"{i}. {movie['title']}\n")
            self.mood_results.insert(tk.END, f"   Genres: {genres}\n")
            self.mood_results.insert(tk.END, f"   Director: {directors}\n")
            self.mood_results.insert(tk.END, f"   Cast: {cast}\n")
            self.mood_results.insert(tk.END, f"   Rating: {movie['vote_average']}\n\n")

# Main function
if __name__ == "__main__":
    root = tk.Tk()
    app = MovieRecommenderGUI(root)
    root.mainloop()