"""
Comprehensive Error Handling and Validation Module
Provides robust error handling, input validation, and logging utilities.
"""

import logging
import functools
import traceback
import sys
from typing import Any, Callable, Dict, List, Optional, Union
from datetime import datetime
import os


class MovieRecommenderLogger:
    """
    Centralized logging system for the movie recommender application.
    """
    
    def __init__(self, log_level: str = 'INFO', log_file: Optional[str] = None):
        """
        Initialize the logger.
        
        Args:
            log_level (str): Logging level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
            log_file (Optional[str]): Path to log file (None for console only)
        """
        self.logger = logging.getLogger('MovieRecommender')
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler (if specified)
        if log_file:
            try:
                # Create logs directory if it doesn't exist
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                
                file_handler = logging.FileHandler(log_file)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                self.logger.warning(f"Could not create file handler: {e}")
    
    def get_logger(self):
        """Get the configured logger instance"""
        return self.logger


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


class RecommenderError(Exception):
    """Custom exception for recommender-specific errors"""
    pass


class InputValidator:
    """
    Comprehensive input validation for movie recommender system.
    """
    
    @staticmethod
    def validate_string(value: Any, field_name: str, min_length: int = 1, max_length: int = 1000) -> str:
        """
        Validate string input.
        
        Args:
            value: Input value to validate
            field_name (str): Name of the field for error messages
            min_length (int): Minimum string length
            max_length (int): Maximum string length
            
        Returns:
            str: Validated string
            
        Raises:
            ValidationError: If validation fails
        """
        if value is None:
            raise ValidationError(f"{field_name} cannot be None")
        
        if not isinstance(value, str):
            try:
                value = str(value)
            except Exception:
                raise ValidationError(f"{field_name} must be a string or convertible to string")
        
        value = value.strip()
        
        if len(value) < min_length:
            raise ValidationError(f"{field_name} must be at least {min_length} characters long")
        
        if len(value) > max_length:
            raise ValidationError(f"{field_name} must be no more than {max_length} characters long")
        
        return value
    
    @staticmethod
    def validate_integer(value: Any, field_name: str, min_value: int = None, max_value: int = None) -> int:
        """
        Validate integer input.
        
        Args:
            value: Input value to validate
            field_name (str): Name of the field for error messages
            min_value (int): Minimum allowed value
            max_value (int): Maximum allowed value
            
        Returns:
            int: Validated integer
            
        Raises:
            ValidationError: If validation fails
        """
        if value is None:
            raise ValidationError(f"{field_name} cannot be None")
        
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} must be a valid integer")
        
        if min_value is not None and value < min_value:
            raise ValidationError(f"{field_name} must be at least {min_value}")
        
        if max_value is not None and value > max_value:
            raise ValidationError(f"{field_name} must be no more than {max_value}")
        
        return value
    
    @staticmethod
    def validate_float(value: Any, field_name: str, min_value: float = None, max_value: float = None) -> float:
        """
        Validate float input.
        
        Args:
            value: Input value to validate
            field_name (str): Name of the field for error messages
            min_value (float): Minimum allowed value
            max_value (float): Maximum allowed value
            
        Returns:
            float: Validated float
            
        Raises:
            ValidationError: If validation fails
        """
        if value is None:
            raise ValidationError(f"{field_name} cannot be None")
        
        try:
            value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} must be a valid number")
        
        if min_value is not None and value < min_value:
            raise ValidationError(f"{field_name} must be at least {min_value}")
        
        if max_value is not None and value > max_value:
            raise ValidationError(f"{field_name} must be no more than {max_value}")
        
        return value
    
    @staticmethod
    def validate_rating(rating: Any) -> float:
        """Validate movie rating (1-5 scale)"""
        return InputValidator.validate_float(rating, "Rating", min_value=1.0, max_value=5.0)
    
    @staticmethod
    def validate_movie_title(title: Any) -> str:
        """Validate movie title"""
        return InputValidator.validate_string(title, "Movie title", min_length=1, max_length=500)
    
    @staticmethod
    def validate_username(username: Any) -> str:
        """Validate username"""
        username = InputValidator.validate_string(username, "Username", min_length=3, max_length=50)
        
        # Additional username validation
        if not username.replace('_', '').replace('-', '').isalnum():
            raise ValidationError("Username can only contain letters, numbers, underscores, and hyphens")
        
        return username
    
    @staticmethod
    def validate_top_n(top_n: Any) -> int:
        """Validate top_n parameter for recommendations"""
        return InputValidator.validate_integer(top_n, "Number of recommendations", min_value=1, max_value=100)
    
    @staticmethod
    def validate_mood(mood: Any) -> str:
        """Validate mood parameter"""
        valid_moods = ['happy', 'sad', 'exciting', 'scary', 'thoughtful', 'relaxing', 'romantic', 'adventurous']
        mood = InputValidator.validate_string(mood, "Mood").lower()
        
        if mood not in valid_moods:
            raise ValidationError(f"Mood must be one of: {', '.join(valid_moods)}")
        
        return mood
    
    @staticmethod
    def validate_search_strategy(strategy: Any) -> str:
        """Validate search strategy parameter"""
        valid_strategies = ['exact', 'fuzzy', 'smart']
        strategy = InputValidator.validate_string(strategy, "Search strategy").lower()
        
        if strategy not in valid_strategies:
            raise ValidationError(f"Search strategy must be one of: {', '.join(valid_strategies)}")
        
        return strategy


def handle_errors(logger: logging.Logger = None, return_on_error: Any = None):
    """
    Decorator for comprehensive error handling.
    
    Args:
        logger: Logger instance to use
        return_on_error: Value to return on error (default: None)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ValidationError as e:
                if logger:
                    logger.warning(f"Validation error in {func.__name__}: {str(e)}")
                return {'error': f"Validation error: {str(e)}", 'type': 'validation'}
            except RecommenderError as e:
                if logger:
                    logger.error(f"Recommender error in {func.__name__}: {str(e)}")
                return {'error': f"Recommender error: {str(e)}", 'type': 'recommender'}
            except FileNotFoundError as e:
                if logger:
                    logger.error(f"File not found in {func.__name__}: {str(e)}")
                return {'error': f"Required file not found: {str(e)}", 'type': 'file_not_found'}
            except MemoryError as e:
                if logger:
                    logger.critical(f"Memory error in {func.__name__}: {str(e)}")
                return {'error': "Insufficient memory to complete operation", 'type': 'memory'}
            except Exception as e:
                if logger:
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
                    logger.debug(f"Traceback: {traceback.format_exc()}")
                return {'error': f"Unexpected error: {str(e)}", 'type': 'unexpected'}
        
        return wrapper
    return decorator


def validate_inputs(**validators):
    """
    Decorator for input validation using specified validators.
    
    Args:
        **validators: Mapping of parameter names to validator functions
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get function signature
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Validate specified parameters
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    try:
                        bound_args.arguments[param_name] = validator(bound_args.arguments[param_name])
                    except ValidationError as e:
                        raise ValidationError(f"Parameter '{param_name}': {str(e)}")
            
            return func(*bound_args.args, **bound_args.kwargs)
        
        return wrapper
    return decorator


class SafeOperations:
    """
    Safe operations with built-in error handling and validation.
    """
    
    def __init__(self, logger: logging.Logger = None):
        """
        Initialize safe operations.
        
        Args:
            logger: Logger instance to use
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def safe_file_operation(self, operation: Callable, *args, **kwargs) -> Dict:
        """
        Safely perform file operations.
        
        Args:
            operation: File operation function to execute
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Dict: Result with success status and data/error
        """
        try:
            result = operation(*args, **kwargs)
            return {'success': True, 'data': result}
        except FileNotFoundError as e:
            self.logger.error(f"File not found: {str(e)}")
            return {'success': False, 'error': f"File not found: {str(e)}", 'type': 'file_not_found'}
        except PermissionError as e:
            self.logger.error(f"Permission denied: {str(e)}")
            return {'success': False, 'error': f"Permission denied: {str(e)}", 'type': 'permission'}
        except OSError as e:
            self.logger.error(f"OS error: {str(e)}")
            return {'success': False, 'error': f"System error: {str(e)}", 'type': 'os_error'}
        except Exception as e:
            self.logger.error(f"Unexpected error in file operation: {str(e)}")
            return {'success': False, 'error': f"Unexpected error: {str(e)}", 'type': 'unexpected'}
    
    def safe_data_operation(self, operation: Callable, *args, **kwargs) -> Dict:
        """
        Safely perform data operations.
        
        Args:
            operation: Data operation function to execute
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Dict: Result with success status and data/error
        """
        try:
            result = operation(*args, **kwargs)
            return {'success': True, 'data': result}
        except MemoryError as e:
            self.logger.critical(f"Memory error: {str(e)}")
            return {'success': False, 'error': "Insufficient memory", 'type': 'memory'}
        except ValueError as e:
            self.logger.error(f"Value error: {str(e)}")
            return {'success': False, 'error': f"Invalid data: {str(e)}", 'type': 'value'}
        except KeyError as e:
            self.logger.error(f"Key error: {str(e)}")
            return {'success': False, 'error': f"Missing data field: {str(e)}", 'type': 'key'}
        except Exception as e:
            self.logger.error(f"Unexpected error in data operation: {str(e)}")
            return {'success': False, 'error': f"Unexpected error: {str(e)}", 'type': 'unexpected'}


# Global logger instance
_global_logger = None

def get_logger(log_level: str = 'INFO', log_file: Optional[str] = None) -> logging.Logger:
    """
    Get the global logger instance.
    
    Args:
        log_level (str): Logging level
        log_file (Optional[str]): Path to log file
        
    Returns:
        logging.Logger: Configured logger instance
    """
    global _global_logger
    if _global_logger is None:
        logger_manager = MovieRecommenderLogger(log_level, log_file)
        _global_logger = logger_manager.get_logger()
    return _global_logger


# Example usage and testing
if __name__ == "__main__":
    # Test logging
    logger = get_logger('DEBUG', 'logs/test.log')
    logger.info("Testing error handling module")
    
    # Test validation
    validator = InputValidator()
    
    try:
        # Valid inputs
        title = validator.validate_movie_title("Avatar")
        rating = validator.validate_rating(4.5)
        username = validator.validate_username("test_user")
        print(f"Valid inputs: title='{title}', rating={rating}, username='{username}'")
        
        # Invalid inputs
        validator.validate_rating(6.0)  # Should raise ValidationError
    except ValidationError as e:
        print(f"Validation error (expected): {e}")
    
    # Test error handling decorator
    @handle_errors(logger)
    def test_function(x):
        if x < 0:
            raise ValueError("Negative value not allowed")
        return x * 2
    
    print(f"Test function with valid input: {test_function(5)}")
    print(f"Test function with invalid input: {test_function(-1)}")
    
    print("Error handling module test completed")
