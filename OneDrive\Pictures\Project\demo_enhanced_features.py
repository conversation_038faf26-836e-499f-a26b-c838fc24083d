"""
Demonstration of Enhanced Movie Recommender Features
Shows all the implemented improvements working together.
"""

import time
import os
from enhanced_recommender import EnhancedMovieRecommender
from error_handler import get_logger, InputValidator, ValidationError


def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_subsection(title: str):
    """Print a formatted subsection header"""
    print(f"\n--- {title} ---")


def demo_caching_performance():
    """Demonstrate caching performance improvements"""
    print_section("CACHING PERFORMANCE DEMONSTRATION")
    
    # First load (no cache)
    print("First load (building cache)...")
    start_time = time.time()
    recommender1 = EnhancedMovieRecommender()
    first_load_time = time.time() - start_time
    print(f"First load time: {first_load_time:.2f} seconds")
    
    # Show cache info
    cache_info = recommender1.get_cache_info()
    print("\nCache files created:")
    for cache_name, info in cache_info.items():
        if info['exists']:
            print(f"  {cache_name}: {info['size_mb']} MB")
    
    # Second load (with cache)
    print("\nSecond load (using cache)...")
    start_time = time.time()
    recommender2 = EnhancedMovieRecommender()
    second_load_time = time.time() - start_time
    print(f"Second load time: {second_load_time:.2f} seconds")
    
    # Performance improvement
    improvement = ((first_load_time - second_load_time) / first_load_time) * 100
    print(f"\nPerformance improvement: {improvement:.1f}% faster!")
    
    return recommender2


def demo_fuzzy_search(recommender):
    """Demonstrate fuzzy search capabilities"""
    print_section("FUZZY SEARCH DEMONSTRATION")
    
    test_queries = [
        "avater",           # Typo in "Avatar"
        "terminator",       # Partial match
        "star wars",        # Common phrase
        "jurrasic park",    # Typo in "Jurassic Park"
        "lord rings",       # Missing words
        "nonexistent movie" # No match
    ]
    
    for query in test_queries:
        print_subsection(f"Searching for: '{query}'")
        
        result = recommender.search_movies(query, strategy='smart', max_results=3)
        
        if result.get('error'):
            print(f"Error: {result['error']}")
        elif result['count'] > 0:
            print(f"Found {result['count']} matches:")
            for i, movie in enumerate(result['matches'][:3], 1):
                print(f"  {i}. {movie['title']} ({movie.get('release_date', 'N/A')})")
        else:
            print("No matches found")


def demo_similar_movies(recommender):
    """Demonstrate similar movie recommendations"""
    print_section("SIMILAR MOVIES DEMONSTRATION")
    
    test_movies = ["Avatar", "The Dark Knight", "Inception"]
    
    for movie_title in test_movies:
        print_subsection(f"Movies similar to '{movie_title}'")
        
        result = recommender.get_similar_movies(movie_title, top_n=5)
        
        if result.get('error'):
            print(f"Error: {result['error']}")
            if 'suggestions' in result:
                print(f"Suggestions: {', '.join(result['suggestions'][:3])}")
        else:
            print(f"Top 5 similar movies:")
            for movie in result['similar_movies']:
                print(f"  {movie['rank']}. {movie['title']} (similarity: {movie['similarity_score']})")


def demo_mood_recommendations(recommender):
    """Demonstrate mood-based recommendations"""
    print_section("MOOD-BASED RECOMMENDATIONS")
    
    moods = ["exciting", "happy", "scary", "thoughtful"]
    
    for mood in moods:
        print_subsection(f"Movies for '{mood}' mood")
        
        result = recommender.get_movies_by_mood(mood, top_n=5)
        
        if result.get('error'):
            print(f"Error: {result['error']}")
        else:
            print(f"Top 5 {mood} movies:")
            for movie in result['movies']:
                genres = ', '.join(movie['genres'][:3]) if movie['genres'] else 'N/A'
                print(f"  {movie['rank']}. {movie['title']} (genres: {genres})")


def demo_user_profiles(recommender):
    """Demonstrate user profile functionality"""
    print_section("USER PROFILE DEMONSTRATION")
    
    # Create a test user
    username = "demo_user"
    print(f"Creating user profile: {username}")
    
    if recommender.create_user_profile(username):
        print("✓ User profile created successfully")
        
        # Rate some movies
        print_subsection("Rating movies")
        movies_to_rate = [
            ("Avatar", 5.0),
            ("The Dark Knight", 4.5),
            ("Inception", 4.8),
            ("Titanic", 3.5)
        ]
        
        for movie, rating in movies_to_rate:
            if recommender.rate_movie(movie, rating):
                print(f"✓ Rated '{movie}': {rating}/5")
            else:
                print(f"✗ Failed to rate '{movie}'")
        
        # Get personalized recommendations
        print_subsection("Personalized recommendations")
        result = recommender.get_personalized_recommendations(top_n=5)
        
        if result.get('error'):
            print(f"Error: {result['error']}")
        else:
            print(f"Personalized recommendations for {result['user']}:")
            for movie in result['recommendations']:
                print(f"  {movie['rank']}. {movie['title']} (rating: {movie['vote_average']}/10)")
    else:
        print("✗ Failed to create user profile")


def demo_error_handling():
    """Demonstrate error handling and validation"""
    print_section("ERROR HANDLING DEMONSTRATION")
    
    validator = InputValidator()
    
    # Test input validation
    print_subsection("Input Validation Tests")
    
    test_cases = [
        ("Valid movie title", lambda: validator.validate_movie_title("Avatar")),
        ("Empty movie title", lambda: validator.validate_movie_title("")),
        ("Valid rating", lambda: validator.validate_rating(4.5)),
        ("Invalid rating (too high)", lambda: validator.validate_rating(6.0)),
        ("Valid username", lambda: validator.validate_username("test_user")),
        ("Invalid username (too short)", lambda: validator.validate_username("ab")),
        ("Valid mood", lambda: validator.validate_mood("happy")),
        ("Invalid mood", lambda: validator.validate_mood("invalid_mood"))
    ]
    
    for test_name, test_func in test_cases:
        try:
            result = test_func()
            print(f"✓ {test_name}: {result}")
        except ValidationError as e:
            print(f"✗ {test_name}: {e}")
        except Exception as e:
            print(f"✗ {test_name}: Unexpected error - {e}")


def demo_statistics(recommender):
    """Show database statistics"""
    print_section("DATABASE STATISTICS")
    
    stats = recommender.get_movie_stats()
    
    if stats.get('error'):
        print(f"Error getting stats: {stats['error']}")
    else:
        print(f"Total movies: {stats['total_movies']:,}")
        print(f"Average rating: {stats['average_rating']}/10")
        print(f"Total genres: {stats['total_genres']}")
        print(f"Date range: {stats['date_range']['earliest']} to {stats['date_range']['latest']}")
        
        print_subsection("Cache Status")
        for cache_name, info in stats['cache_info'].items():
            if info['exists']:
                status = "✓ Valid" if info['valid'] else "✗ Expired"
                print(f"  {cache_name}: {info['size_mb']} MB - {status}")
            else:
                print(f"  {cache_name}: Not cached")


def main():
    """Main demonstration function"""
    print("Enhanced Movie Recommender System - Feature Demonstration")
    print("This demo showcases all the implemented improvements:")
    print("1. Data Caching System")
    print("2. Fuzzy Search for Movie Titles") 
    print("3. Unified Data Manager")
    print("4. Comprehensive Error Handling")
    
    # Initialize logger
    logger = get_logger('INFO', 'logs/demo.log')
    logger.info("Starting enhanced features demonstration")
    
    try:
        # Demo 1: Caching Performance
        recommender = demo_caching_performance()
        
        # Demo 2: Fuzzy Search
        demo_fuzzy_search(recommender)
        
        # Demo 3: Similar Movies
        demo_similar_movies(recommender)
        
        # Demo 4: Mood-based Recommendations
        demo_mood_recommendations(recommender)
        
        # Demo 5: User Profiles
        demo_user_profiles(recommender)
        
        # Demo 6: Error Handling
        demo_error_handling()
        
        # Demo 7: Statistics
        demo_statistics(recommender)
        
        print_section("DEMONSTRATION COMPLETED")
        print("All enhanced features have been successfully demonstrated!")
        print("\nKey improvements implemented:")
        print("✓ Intelligent caching for faster startup")
        print("✓ Fuzzy search with typo tolerance")
        print("✓ Unified data management")
        print("✓ Comprehensive error handling")
        print("✓ Input validation")
        print("✓ Structured logging")
        print("✓ Enhanced user experience")
        
        logger.info("Enhanced features demonstration completed successfully")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    main()
