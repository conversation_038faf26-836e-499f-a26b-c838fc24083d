---
description: Repository Information Overview
alwaysApply: true
---

# Enhanced Movie Recommendation System Information

## Summary
An enhanced Python-based movie recommendation system that analyzes movie data from TMDB (The Movie Database) to provide personalized movie recommendations. The system uses natural language processing and machine learning techniques to find similar movies based on features like genres, keywords, plot descriptions, cast, and directors. It also offers functionality to display top-rated, popular, and latest movies, as well as mood-based recommendations.

## Structure
- `recommender.py`: Main Python script with the enhanced recommendation system implementation
- `Untitled.ipynb`: Jupyter notebook with interactive development and testing of the recommendation system
- `tmdb_5000_movies.csv`: Dataset containing movie information (titles, genres, keywords, etc.)
- `tmdb_5000_credits.csv`: Dataset containing movie credits information
- `requirements.txt`: List of Python package dependencies

## Language & Runtime
**Language**: Python
**Version**: Python 3.x
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- pandas>=1.0.0: Data manipulation and analysis
- scikit-learn>=0.24.0: Machine learning algorithms (TF-IDF, KNN, cosine similarity)
- numpy>=1.19.0: Numerical operations
- matplotlib>=3.3.0: Data visualization
- jupyter>=1.0.0: For notebook development

## Data Processing
**Data Sources**:
- TMDB 5000 Movie Dataset (tmdb_5000_movies.csv, tmdb_5000_credits.csv)

**Processing Pipeline**:
1. Data loading and merging from CSV files
2. Feature extraction from JSON-formatted strings (genres, keywords, cast, directors)
3. Text preprocessing and enhanced feature engineering
4. TF-IDF vectorization of text features
5. Similarity calculation using cosine similarity or KNN

## Usage
**Installation**:
```bash
pip install -r requirements.txt
```

**Running the Script**:
```bash
python recommender.py
```

**Interactive Features**:
- View top-rated movies based on weighted rating algorithm
- Browse most popular movies based on popularity score
- Discover latest movies sorted by release date
- Find similar movies to a specified title using content-based filtering
- Get movie recommendations based on your mood (happy, sad, exciting, etc.)

## Implementation Details
**Recommendation Algorithms**:
- Content-based filtering using TF-IDF vectorization with cast and director information
- K-Nearest Neighbors for finding similar movies
- Weighted rating calculation for top-rated movies
- Mood-based filtering using genre mapping

**Key Functions**:
- `get_similar_movies()`: Finds movies similar to a given title with improved title matching
- `get_top_rated_movies()`: Returns highest-rated movies using weighted rating
- `get_popular_movies()`: Returns most popular movies by popularity score
- `get_latest_movies()`: Returns most recent movies by release date
- `get_movies_by_mood()`: Returns movies matching a specified mood or emotional tone