"""
Fuzzy Search Module for Movie Recommender System
Provides intelligent movie title matching with typo tolerance and partial matching.
"""

import difflib
import re
import logging
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)


class FuzzyMovieSearch:
    """
    Advanced fuzzy search for movie titles with multiple matching strategies.
    Handles typos, partial matches, and provides ranked results.
    """
    
    def __init__(self, movie_titles: List[str], similarity_threshold: float = 0.6):
        """
        Initialize fuzzy search with movie titles.
        
        Args:
            movie_titles (List[str]): List of all available movie titles
            similarity_threshold (float): Minimum similarity score for matches (0.0 to 1.0)
        """
        self.movie_titles = movie_titles
        self.similarity_threshold = similarity_threshold
        
        # Create normalized versions for better matching
        self.normalized_titles = [self._normalize_title(title) for title in movie_titles]
        
        # Create mapping from normalized to original titles
        self.title_mapping = dict(zip(self.normalized_titles, movie_titles))
        
        logger.info(f"Fuzzy search initialized with {len(movie_titles)} movies")
    
    def _normalize_title(self, title: str) -> str:
        """
        Normalize title for better matching.
        
        Args:
            title (str): Original movie title
            
        Returns:
            str: Normalized title
        """
        if not title:
            return ""
        
        # Convert to lowercase
        normalized = title.lower()
        
        # Remove common articles and words that don't affect matching
        articles = ['the', 'a', 'an']
        words = normalized.split()
        filtered_words = [word for word in words if word not in articles]
        
        # Join back and remove special characters except spaces
        normalized = ' '.join(filtered_words)
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        # Remove extra whitespace
        normalized = ' '.join(normalized.split())
        
        return normalized
    
    def _calculate_similarity(self, query: str, target: str) -> float:
        """
        Calculate similarity between query and target using multiple methods.
        
        Args:
            query (str): Search query
            target (str): Target movie title
            
        Returns:
            float: Similarity score (0.0 to 1.0)
        """
        # Method 1: Sequence matching (handles typos well)
        seq_similarity = difflib.SequenceMatcher(None, query, target).ratio()
        
        # Method 2: Substring matching (handles partial matches)
        if query in target:
            substring_similarity = len(query) / len(target)
        elif target in query:
            substring_similarity = len(target) / len(query)
        else:
            substring_similarity = 0.0
        
        # Method 3: Word-based matching (handles word order differences)
        query_words = set(query.split())
        target_words = set(target.split())
        
        if query_words and target_words:
            word_intersection = len(query_words.intersection(target_words))
            word_union = len(query_words.union(target_words))
            word_similarity = word_intersection / word_union if word_union > 0 else 0.0
        else:
            word_similarity = 0.0
        
        # Combine similarities with weights
        combined_similarity = (
            0.5 * seq_similarity +
            0.3 * substring_similarity +
            0.2 * word_similarity
        )
        
        return combined_similarity
    
    def search(self, query: str, max_results: int = 5) -> List[Tuple[str, float]]:
        """
        Search for movies matching the query.
        
        Args:
            query (str): Search query (movie title or partial title)
            max_results (int): Maximum number of results to return
            
        Returns:
            List[Tuple[str, float]]: List of (movie_title, similarity_score) tuples
        """
        if not query or not query.strip():
            return []
        
        # Normalize the query
        normalized_query = self._normalize_title(query)
        
        # Calculate similarities for all titles
        similarities = []
        for normalized_title, original_title in self.title_mapping.items():
            similarity = self._calculate_similarity(normalized_query, normalized_title)
            
            if similarity >= self.similarity_threshold:
                similarities.append((original_title, similarity))
        
        # Sort by similarity score (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Return top results
        return similarities[:max_results]
    
    def find_best_match(self, query: str) -> Optional[str]:
        """
        Find the single best match for a query.
        
        Args:
            query (str): Search query
            
        Returns:
            Optional[str]: Best matching movie title or None if no good match
        """
        results = self.search(query, max_results=1)
        
        if results:
            return results[0][0]
        return None
    
    def search_with_suggestions(self, query: str, max_results: int = 5) -> dict:
        """
        Search with additional suggestions and metadata.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results
            
        Returns:
            dict: Search results with metadata
        """
        results = self.search(query, max_results)
        
        # Check for exact matches
        exact_matches = [title for title in self.movie_titles 
                        if title.lower() == query.lower()]
        
        # Check for starts-with matches
        starts_with = [title for title in self.movie_titles 
                      if title.lower().startswith(query.lower())]
        
        # Check for contains matches
        contains = [title for title in self.movie_titles 
                   if query.lower() in title.lower() and title not in starts_with]
        
        return {
            'query': query,
            'exact_matches': exact_matches,
            'starts_with': starts_with[:max_results],
            'contains': contains[:max_results],
            'fuzzy_matches': results,
            'total_found': len(results),
            'has_results': len(results) > 0 or len(exact_matches) > 0
        }
    
    def get_search_suggestions(self, query: str, max_suggestions: int = 10) -> List[str]:
        """
        Get search suggestions for autocomplete functionality.
        
        Args:
            query (str): Partial query
            max_suggestions (int): Maximum suggestions to return
            
        Returns:
            List[str]: List of suggested movie titles
        """
        if not query or len(query) < 2:
            return []
        
        suggestions = []
        query_lower = query.lower()
        
        # Priority 1: Titles that start with the query
        for title in self.movie_titles:
            if title.lower().startswith(query_lower):
                suggestions.append(title)
        
        # Priority 2: Titles that contain the query
        for title in self.movie_titles:
            if query_lower in title.lower() and title not in suggestions:
                suggestions.append(title)
        
        # Priority 3: Fuzzy matches
        fuzzy_results = self.search(query, max_results=max_suggestions)
        for title, _ in fuzzy_results:
            if title not in suggestions:
                suggestions.append(title)
        
        return suggestions[:max_suggestions]
    
    def update_titles(self, new_titles: List[str]):
        """
        Update the list of searchable movie titles.
        
        Args:
            new_titles (List[str]): New list of movie titles
        """
        self.movie_titles = new_titles
        self.normalized_titles = [self._normalize_title(title) for title in new_titles]
        self.title_mapping = dict(zip(self.normalized_titles, new_titles))
        
        logger.info(f"Fuzzy search updated with {len(new_titles)} movies")


class SmartMovieSearcher:
    """
    High-level interface for smart movie searching with multiple strategies.
    """
    
    def __init__(self, movies_df):
        """
        Initialize with movie dataframe.
        
        Args:
            movies_df: Pandas DataFrame containing movie data
        """
        self.movies_df = movies_df
        self.fuzzy_search = FuzzyMovieSearch(movies_df['title'].tolist())
    
    def search_movie(self, query: str, strategy: str = 'smart') -> dict:
        """
        Search for a movie using specified strategy.
        
        Args:
            query (str): Search query
            strategy (str): Search strategy ('exact', 'fuzzy', 'smart')
            
        Returns:
            dict: Search results with movie information
        """
        if strategy == 'exact':
            return self._exact_search(query)
        elif strategy == 'fuzzy':
            return self._fuzzy_search(query)
        else:  # smart strategy
            return self._smart_search(query)
    
    def _exact_search(self, query: str) -> dict:
        """Exact title matching"""
        matches = self.movies_df[self.movies_df['title'].str.lower() == query.lower()]
        
        return {
            'strategy': 'exact',
            'query': query,
            'matches': matches.to_dict('records') if not matches.empty else [],
            'count': len(matches)
        }
    
    def _fuzzy_search(self, query: str) -> dict:
        """Fuzzy matching with similarity scores"""
        results = self.fuzzy_search.search_with_suggestions(query)
        
        # Get full movie data for matches
        all_matches = []
        for match_list in [results['exact_matches'], results['starts_with'], 
                          results['contains'], [m[0] for m in results['fuzzy_matches']]]:
            for title in match_list:
                movie_data = self.movies_df[self.movies_df['title'] == title]
                if not movie_data.empty:
                    all_matches.append(movie_data.iloc[0].to_dict())
        
        # Remove duplicates while preserving order
        seen_titles = set()
        unique_matches = []
        for match in all_matches:
            if match['title'] not in seen_titles:
                unique_matches.append(match)
                seen_titles.add(match['title'])
        
        return {
            'strategy': 'fuzzy',
            'query': query,
            'matches': unique_matches,
            'count': len(unique_matches),
            'suggestions': results
        }
    
    def _smart_search(self, query: str) -> dict:
        """Smart search that tries exact first, then fuzzy"""
        # Try exact search first
        exact_result = self._exact_search(query)
        
        if exact_result['count'] > 0:
            return exact_result
        
        # Fall back to fuzzy search
        fuzzy_result = self._fuzzy_search(query)
        fuzzy_result['strategy'] = 'smart'
        
        return fuzzy_result
    
    def get_movie_by_title(self, title: str, use_fuzzy: bool = True) -> Optional[dict]:
        """
        Get a single movie by title with optional fuzzy matching.
        
        Args:
            title (str): Movie title to search for
            use_fuzzy (bool): Whether to use fuzzy matching if exact match fails
            
        Returns:
            Optional[dict]: Movie data or None if not found
        """
        # Try exact match first
        exact_match = self.movies_df[self.movies_df['title'].str.lower() == title.lower()]
        
        if not exact_match.empty:
            return exact_match.iloc[0].to_dict()
        
        # Try fuzzy matching if enabled
        if use_fuzzy:
            best_match = self.fuzzy_search.find_best_match(title)
            if best_match:
                movie_data = self.movies_df[self.movies_df['title'] == best_match]
                if not movie_data.empty:
                    return movie_data.iloc[0].to_dict()
        
        return None
