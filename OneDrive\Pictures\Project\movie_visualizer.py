import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import seaborn as sns

class MovieVisualizer:
    """Class for visualizing movie relationships and recommendations"""
    
    def __init__(self, movies_df, tfidf_matrix=None):
        """Initialize with movie data"""
        self.movies_df = movies_df
        self.tfidf_matrix = tfidf_matrix
    
    def plot_genre_distribution(self, save_path=None):
        """Plot distribution of movie genres"""
        # Extract all genres
        all_genres = []
        for genres in self.movies_df['genres']:
            all_genres.extend(genres)
        
        # Count genre occurrences
        genre_counts = pd.Series(all_genres).value_counts()
        
        # Plot top 15 genres
        plt.figure(figsize=(12, 8))
        sns.barplot(x=genre_counts.index[:15], y=genre_counts.values[:15])
        plt.title('Top 15 Movie Genres', fontsize=16)
        plt.xlabel('Genre', fontsize=14)
        plt.ylabel('Count', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def plot_rating_vs_popularity(self, save_path=None):
        """Plot movie ratings vs popularity"""
        plt.figure(figsize=(12, 8))
        
        # Create scatter plot
        plt.scatter(
            self.movies_df['vote_average'], 
            self.movies_df['popularity'],
            alpha=0.5
        )
        
        plt.title('Movie Ratings vs Popularity', fontsize=16)
        plt.xlabel('Rating', fontsize=14)
        plt.ylabel('Popularity', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Add trend line
        z = np.polyfit(self.movies_df['vote_average'], self.movies_df['popularity'], 1)
        p = np.poly1d(z)
        plt.plot(self.movies_df['vote_average'], p(self.movies_df['vote_average']), "r--", alpha=0.8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def plot_movie_similarity_map(self, movie_title, num_similar=10, save_path=None):
        """
        Plot a 2D map of movie similarities using t-SNE
        
        Args:
            movie_title: Title of the movie to find similarities for
            num_similar: Number of similar movies to include
            save_path: Path to save the plot (if None, display instead)
        """
        if self.tfidf_matrix is None:
            print("Error: TF-IDF matrix not provided")
            return None
        
        # Find movie index
        movie_indices = self.movies_df[self.movies_df['title'] == movie_title].index
        if len(movie_indices) == 0:
            print(f"Movie '{movie_title}' not found")
            return None
        
        movie_idx = movie_indices[0]
        
        # Get similar movies
        from sklearn.metrics.pairwise import linear_kernel
        cosine_sim = linear_kernel(self.tfidf_matrix, self.tfidf_matrix)
        sim_scores = list(enumerate(cosine_sim[movie_idx]))
        sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)
        sim_scores = sim_scores[:num_similar+1]  # Include the movie itself
        movie_indices = [i[0] for i in sim_scores]
        
        # Get subset of TF-IDF matrix for these movies
        tfidf_subset = self.tfidf_matrix[movie_indices]
        
        # Reduce dimensions for visualization
        # First use PCA to reduce to 50 dimensions (for speed)
        pca = PCA(n_components=min(50, tfidf_subset.shape[0]-1))
        reduced_features = pca.fit_transform(tfidf_subset.toarray())
        
        # Then use t-SNE to reduce to 2 dimensions
        tsne = TSNE(n_components=2, perplexity=min(5, num_similar-1), 
                   random_state=42, n_iter=1000)
        tsne_features = tsne.fit_transform(reduced_features)
        
        # Create dataframe for plotting
        plot_df = pd.DataFrame({
            'x': tsne_features[:, 0],
            'y': tsne_features[:, 1],
            'title': self.movies_df['title'].iloc[movie_indices],
            'similarity': [score for _, score in sim_scores]
        })
        
        # Plot
        plt.figure(figsize=(12, 10))
        
        # Plot points
        scatter = plt.scatter(
            plot_df['x'], 
            plot_df['y'],
            s=100 * plot_df['similarity'],  # Size based on similarity
            alpha=0.7,
            c=plot_df['similarity'],  # Color based on similarity
            cmap='viridis'
        )
        
        # Add colorbar
        plt.colorbar(scatter, label='Similarity Score')
        
        # Add labels
        for i, row in plot_df.iterrows():
            plt.annotate(
                row['title'],
                (row['x'], row['y']),
                fontsize=9,
                ha='center',
                va='center',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
            )
        
        # Highlight the input movie
        input_movie_idx = plot_df[plot_df['title'] == movie_title].index[0]
        plt.scatter(
            plot_df.loc[input_movie_idx, 'x'],
            plot_df.loc[input_movie_idx, 'y'],
            s=200,
            facecolors='none',
            edgecolors='red',
            linewidth=2
        )
        
        plt.title(f'Movies Similar to "{movie_title}"', fontsize=16)
        plt.xlabel('t-SNE Feature 1', fontsize=14)
        plt.ylabel('t-SNE Feature 2', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def plot_genre_radar_chart(self, movie_titles, save_path=None):
        """
        Plot a radar chart comparing genres of multiple movies
        
        Args:
            movie_titles: List of movie titles to compare
            save_path: Path to save the plot (if None, display instead)
        """
        # Get movies
        movies = self.movies_df[self.movies_df['title'].isin(movie_titles)]
        
        if len(movies) == 0:
            print("No movies found with the provided titles")
            return None
        
        # Get all unique genres from these movies
        all_genres = set()
        for genres in movies['genres']:
            all_genres.update(genres)
        
        all_genres = sorted(list(all_genres))
        
        # Create genre matrix (movies x genres)
        genre_matrix = np.zeros((len(movies), len(all_genres)))
        
        for i, (_, movie) in enumerate(movies.iterrows()):
            for j, genre in enumerate(all_genres):
                if genre in movie['genres']:
                    genre_matrix[i, j] = 1
        
        # Plot radar chart
        angles = np.linspace(0, 2*np.pi, len(all_genres), endpoint=False).tolist()
        angles += angles[:1]  # Close the loop
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True))
        
        # Add genre labels
        plt.xticks(angles[:-1], all_genres, fontsize=12)
        
        # Plot each movie
        for i, title in enumerate(movies['title']):
            values = genre_matrix[i].tolist()
            values += values[:1]  # Close the loop
            
            ax.plot(angles, values, linewidth=2, label=title)
            ax.fill(angles, values, alpha=0.1)
        
        plt.title('Genre Comparison', fontsize=16)
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def plot_user_preferences(self, user_profile, save_path=None):
        """
        Plot a user's genre preferences based on their profile
        
        Args:
            user_profile: UserProfile object
            save_path: Path to save the plot (if None, display instead)
        """
        # Get genre preferences
        genre_prefs = user_profile.get_genre_preferences()
        
        if not genre_prefs:
            print("No genre preferences found in user profile")
            return None
        
        # Convert to Series for plotting
        prefs_series = pd.Series(genre_prefs)
        prefs_series = prefs_series.sort_values(ascending=False)
        
        # Plot
        plt.figure(figsize=(12, 8))
        
        # Create bar chart
        sns.barplot(x=prefs_series.index, y=prefs_series.values)
        
        plt.title(f'Genre Preferences for {user_profile.username}', fontsize=16)
        plt.xlabel('Genre', fontsize=14)
        plt.ylabel('Preference Score', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return save_path
        else:
            plt.show()
            return None


# Example usage
if __name__ == "__main__":
    # Load data
    try:
        movies_df = pd.read_csv('tmdb_5000_movies.csv')
        credits_df = pd.read_csv('tmdb_5000_credits.csv')
        movies_df = movies_df.merge(credits_df, on='title')
        
        # Parse genres
        def parse_genres(genres_str):
            import ast
            try:
                return [item['name'] for item in ast.literal_eval(genres_str)]
            except:
                return []
        
        movies_df['genres'] = movies_df['genres'].apply(parse_genres)
        
        # Create visualizer
        visualizer = MovieVisualizer(movies_df)
        
        # Plot genre distribution
        visualizer.plot_genre_distribution()
        
        # Plot rating vs popularity
        visualizer.plot_rating_vs_popularity()
        
    except FileNotFoundError:
        print("Error: Movie data files not found.")