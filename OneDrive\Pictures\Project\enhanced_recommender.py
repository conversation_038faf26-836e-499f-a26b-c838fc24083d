"""
Enhanced Movie Recommender System
Integrates data caching, fuzzy search, and improved error handling.
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Optional, Tuple
from data_manager import get_data_manager
from fuzzy_search import SmartMovieSearcher
from user_profiles import UserProfileManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedMovieRecommender:
    """
    Enhanced movie recommender with caching, fuzzy search, and robust error handling.
    """
    
    def __init__(self, cache_expiry_hours: int = 24):
        """
        Initialize the enhanced recommender.
        
        Args:
            cache_expiry_hours (int): Hours after which cache expires
        """
        self.data_manager = get_data_manager()
        self.data_manager.cache_expiry_hours = cache_expiry_hours
        
        self.movie_searcher = None
        self.profile_manager = UserProfileManager()
        self.active_profile = None
        
        # Initialize data
        self._initialize_data()
    
    def _initialize_data(self):
        """Initialize data and components"""
        try:
            logger.info("Initializing Enhanced Movie Recommender...")
            
            # Load data with caching
            if not self.data_manager.load_data():
                raise Exception("Failed to load movie data")
            
            # Initialize movie searcher
            self.movie_searcher = SmartMovieSearcher(self.data_manager.movies_df)
            
            logger.info("Enhanced Movie Recommender initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize recommender: {str(e)}")
            raise
    
    def refresh_data(self):
        """Force refresh of all cached data"""
        try:
            logger.info("Refreshing data...")
            self.data_manager.load_data(force_refresh=True)
            self.movie_searcher = SmartMovieSearcher(self.data_manager.movies_df)
            logger.info("Data refreshed successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to refresh data: {str(e)}")
            return False
    
    def get_cache_info(self) -> Dict:
        """Get information about cached data"""
        return self.data_manager.get_cache_info()
    
    def clear_cache(self):
        """Clear all cached data"""
        self.data_manager.clear_cache()
    
    # === Movie Search Methods ===
    
    def search_movies(self, query: str, strategy: str = 'smart', max_results: int = 10) -> Dict:
        """
        Search for movies with intelligent matching.
        
        Args:
            query (str): Search query
            strategy (str): Search strategy ('exact', 'fuzzy', 'smart')
            max_results (int): Maximum results to return
            
        Returns:
            Dict: Search results with movie information
        """
        try:
            if not query or not query.strip():
                return {'error': 'Empty search query', 'matches': [], 'count': 0}
            
            result = self.movie_searcher.search_movie(query, strategy)
            
            # Limit results
            if result['matches']:
                result['matches'] = result['matches'][:max_results]
                result['count'] = len(result['matches'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error searching movies: {str(e)}")
            return {'error': str(e), 'matches': [], 'count': 0}
    
    def get_movie_by_title(self, title: str, use_fuzzy: bool = True) -> Optional[Dict]:
        """
        Get a single movie by title with optional fuzzy matching.
        
        Args:
            title (str): Movie title
            use_fuzzy (bool): Whether to use fuzzy matching
            
        Returns:
            Optional[Dict]: Movie data or None
        """
        try:
            return self.movie_searcher.get_movie_by_title(title, use_fuzzy)
        except Exception as e:
            logger.error(f"Error getting movie by title: {str(e)}")
            return None
    
    # === Recommendation Methods ===
    
    def get_similar_movies(self, title: str, top_n: int = 10, use_fuzzy: bool = True) -> Dict:
        """
        Find movies similar to the given title.
        
        Args:
            title (str): Movie title
            top_n (int): Number of recommendations
            use_fuzzy (bool): Whether to use fuzzy title matching
            
        Returns:
            Dict: Similar movies with metadata
        """
        try:
            # Find the movie
            movie = self.get_movie_by_title(title, use_fuzzy)
            if not movie:
                return {
                    'error': f"Movie '{title}' not found",
                    'suggestions': self.movie_searcher.fuzzy_search.get_search_suggestions(title),
                    'similar_movies': []
                }
            
            actual_title = movie['title']
            
            # Get movie index
            if actual_title not in self.data_manager.indices:
                return {'error': f"Movie '{actual_title}' not in similarity index", 'similar_movies': []}
            
            idx = self.data_manager.indices[actual_title]
            
            # Calculate similarity scores
            sim_scores = list(enumerate(self.data_manager.cosine_sim[idx]))
            sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)
            
            # Get top similar movies (excluding the input movie)
            sim_scores = sim_scores[1:top_n+1]
            movie_indices = [i[0] for i in sim_scores]
            
            # Get movie details
            similar_movies = []
            for i, movie_idx in enumerate(movie_indices):
                movie_data = self.data_manager.movies_df.iloc[movie_idx]
                similar_movies.append({
                    'rank': i + 1,
                    'title': movie_data['title'],
                    'similarity_score': round(sim_scores[i][1], 3),
                    'genres': movie_data.get('genres', []),
                    'vote_average': movie_data.get('vote_average', 0),
                    'release_date': movie_data.get('release_date', ''),
                    'overview': movie_data.get('overview', '')[:200] + '...' if len(str(movie_data.get('overview', ''))) > 200 else movie_data.get('overview', '')
                })
            
            return {
                'input_movie': {
                    'title': actual_title,
                    'original_query': title
                },
                'similar_movies': similar_movies,
                'count': len(similar_movies)
            }
            
        except Exception as e:
            logger.error(f"Error getting similar movies: {str(e)}")
            return {'error': str(e), 'similar_movies': []}
    
    def get_movies_by_mood(self, mood: str, top_n: int = 10) -> Dict:
        """
        Get movie recommendations based on mood.
        
        Args:
            mood (str): User's mood
            top_n (int): Number of recommendations
            
        Returns:
            Dict: Mood-based recommendations
        """
        try:
            # Mood to genre mapping
            mood_keywords = {
                'happy': ['comedy', 'family', 'animation', 'musical', 'romance'],
                'sad': ['drama', 'tragedy', 'war', 'romance'],
                'exciting': ['action', 'adventure', 'thriller', 'sci-fi', 'fantasy'],
                'scary': ['horror', 'thriller', 'mystery'],
                'thoughtful': ['drama', 'documentary', 'history', 'biography'],
                'relaxing': ['animation', 'family', 'comedy', 'fantasy'],
                'romantic': ['romance', 'drama', 'comedy'],
                'adventurous': ['adventure', 'action', 'fantasy', 'sci-fi']
            }
            
            # Get keywords for the mood
            keywords = mood_keywords.get(mood.lower(), mood_keywords['exciting'])
            
            # Score movies based on genre match
            def calculate_mood_score(movie):
                score = 0
                movie_genres = movie.get('genres', [])
                if isinstance(movie_genres, list):
                    for genre in movie_genres:
                        if any(keyword.lower() in genre.lower() for keyword in keywords):
                            score += 1
                return score
            
            # Calculate scores for all movies
            movies_with_scores = []
            for _, movie in self.data_manager.movies_df.iterrows():
                mood_score = calculate_mood_score(movie)
                if mood_score > 0:  # Only include movies that match the mood
                    movies_with_scores.append({
                        'title': movie['title'],
                        'mood_score': mood_score,
                        'vote_average': movie.get('vote_average', 0),
                        'popularity': movie.get('popularity', 0),
                        'genres': movie.get('genres', []),
                        'release_date': movie.get('release_date', ''),
                        'overview': movie.get('overview', '')[:200] + '...' if len(str(movie.get('overview', ''))) > 200 else movie.get('overview', '')
                    })
            
            # Sort by mood score, then by rating, then by popularity
            movies_with_scores.sort(
                key=lambda x: (x['mood_score'], x['vote_average'], x['popularity']),
                reverse=True
            )
            
            # Add ranking
            for i, movie in enumerate(movies_with_scores[:top_n]):
                movie['rank'] = i + 1
            
            return {
                'mood': mood,
                'keywords': keywords,
                'movies': movies_with_scores[:top_n],
                'count': len(movies_with_scores[:top_n]),
                'total_matches': len(movies_with_scores)
            }
            
        except Exception as e:
            logger.error(f"Error getting movies by mood: {str(e)}")
            return {'error': str(e), 'movies': []}
    
    def get_top_rated_movies(self, top_n: int = 10, min_votes: int = 100) -> List[Dict]:
        """Get top rated movies with minimum vote threshold"""
        try:
            # Filter movies with sufficient votes
            qualified_movies = self.data_manager.movies_df[
                self.data_manager.movies_df['vote_count'] >= min_votes
            ]
            
            # Sort by rating
            top_movies = qualified_movies.nlargest(top_n, 'vote_average')
            
            return [
                {
                    'rank': i + 1,
                    'title': movie['title'],
                    'vote_average': movie['vote_average'],
                    'vote_count': movie['vote_count'],
                    'genres': movie.get('genres', []),
                    'release_date': movie.get('release_date', ''),
                    'overview': movie.get('overview', '')[:200] + '...' if len(str(movie.get('overview', ''))) > 200 else movie.get('overview', '')
                }
                for i, (_, movie) in enumerate(top_movies.iterrows())
            ]
            
        except Exception as e:
            logger.error(f"Error getting top rated movies: {str(e)}")
            return []
    
    def get_popular_movies(self, top_n: int = 10) -> List[Dict]:
        """Get most popular movies"""
        try:
            top_movies = self.data_manager.movies_df.nlargest(top_n, 'popularity')
            
            return [
                {
                    'rank': i + 1,
                    'title': movie['title'],
                    'popularity': movie['popularity'],
                    'vote_average': movie['vote_average'],
                    'genres': movie.get('genres', []),
                    'release_date': movie.get('release_date', ''),
                    'overview': movie.get('overview', '')[:200] + '...' if len(str(movie.get('overview', ''))) > 200 else movie.get('overview', '')
                }
                for i, (_, movie) in enumerate(top_movies.iterrows())
            ]
            
        except Exception as e:
            logger.error(f"Error getting popular movies: {str(e)}")
            return []
    
    # === User Profile Methods ===
    
    def create_user_profile(self, username: str) -> bool:
        """Create a new user profile"""
        try:
            success = self.profile_manager.create_profile(username)
            if success:
                self.active_profile = self.profile_manager.active_profile
                logger.info(f"Created user profile: {username}")
            return success
        except Exception as e:
            logger.error(f"Error creating user profile: {str(e)}")
            return False
    
    def load_user_profile(self, username: str) -> bool:
        """Load an existing user profile"""
        try:
            success = self.profile_manager.load_profile(username)
            if success:
                self.active_profile = self.profile_manager.active_profile
                logger.info(f"Loaded user profile: {username}")
            return success
        except Exception as e:
            logger.error(f"Error loading user profile: {str(e)}")
            return False
    
    def rate_movie(self, title: str, rating: float, use_fuzzy: bool = True) -> bool:
        """Rate a movie"""
        try:
            if not self.active_profile:
                logger.warning("No active user profile for rating")
                return False
            
            movie = self.get_movie_by_title(title, use_fuzzy)
            if not movie:
                logger.warning(f"Movie not found for rating: {title}")
                return False
            
            movie_id = movie.get('id')
            actual_title = movie['title']
            
            return self.active_profile.rate_movie(movie_id, actual_title, rating)
            
        except Exception as e:
            logger.error(f"Error rating movie: {str(e)}")
            return False
    
    def get_personalized_recommendations(self, top_n: int = 10) -> Dict:
        """Get personalized recommendations for active user"""
        try:
            if not self.active_profile:
                return {'error': 'No active user profile', 'recommendations': []}
            
            recommendations = self.active_profile.get_personalized_recommendations(
                self.data_manager.movies_df, top_n
            )
            
            # Format recommendations
            formatted_recs = []
            for i, (_, movie) in enumerate(recommendations.iterrows()):
                formatted_recs.append({
                    'rank': i + 1,
                    'title': movie['title'],
                    'vote_average': movie.get('vote_average', 0),
                    'genres': movie.get('genres', []),
                    'release_date': movie.get('release_date', ''),
                    'overview': movie.get('overview', '')[:200] + '...' if len(str(movie.get('overview', ''))) > 200 else movie.get('overview', '')
                })
            
            return {
                'user': self.active_profile.username,
                'recommendations': formatted_recs,
                'count': len(formatted_recs)
            }
            
        except Exception as e:
            logger.error(f"Error getting personalized recommendations: {str(e)}")
            return {'error': str(e), 'recommendations': []}
    
    # === Utility Methods ===
    
    def get_movie_stats(self) -> Dict:
        """Get statistics about the movie database"""
        try:
            df = self.data_manager.movies_df
            
            return {
                'total_movies': len(df),
                'average_rating': round(df['vote_average'].mean(), 2),
                'total_genres': len(set([genre for genres in df['genres'] for genre in genres if isinstance(genres, list)])),
                'date_range': {
                    'earliest': df['release_date'].min(),
                    'latest': df['release_date'].max()
                },
                'cache_info': self.get_cache_info()
            }
            
        except Exception as e:
            logger.error(f"Error getting movie stats: {str(e)}")
            return {'error': str(e)}


# Example usage and testing
if __name__ == "__main__":
    # Initialize enhanced recommender
    recommender = EnhancedMovieRecommender()
    
    # Test fuzzy search
    print("=== Testing Fuzzy Search ===")
    search_result = recommender.search_movies("avater")  # Typo in "Avatar"
    print(f"Search for 'avater': {len(search_result['matches'])} matches found")
    
    # Test similar movies
    print("\n=== Testing Similar Movies ===")
    similar = recommender.get_similar_movies("Avatar")
    if 'similar_movies' in similar:
        print(f"Similar to Avatar: {len(similar['similar_movies'])} movies")
        for movie in similar['similar_movies'][:3]:
            print(f"  {movie['rank']}. {movie['title']} (similarity: {movie['similarity_score']})")
    
    # Test mood-based recommendations
    print("\n=== Testing Mood-based Recommendations ===")
    mood_recs = recommender.get_movies_by_mood("exciting")
    if 'movies' in mood_recs:
        print(f"Exciting movies: {len(mood_recs['movies'])} found")
        for movie in mood_recs['movies'][:3]:
            print(f"  {movie['rank']}. {movie['title']} (score: {movie['mood_score']})")
    
    # Show cache info
    print("\n=== Cache Information ===")
    cache_info = recommender.get_cache_info()
    for cache_name, info in cache_info.items():
        if info['exists']:
            print(f"{cache_name}: {info['size_mb']} MB, modified: {info['modified']}, valid: {info['valid']}")
        else:
            print(f"{cache_name}: Not cached")
