import os
import json
import pandas as pd
import numpy as np
from collections import Counter

class UserProfile:
    """Class to manage user profiles, viewing history, and preferences"""
    
    def __init__(self, username, profiles_dir="profiles"):
        """Initialize user profile"""
        self.username = username
        self.profiles_dir = profiles_dir
        self.profile_path = os.path.join(profiles_dir, f"{username}.json")
        self.profile = self._load_profile()
    
    def _load_profile(self):
        """Load user profile from file or create a new one"""
        # Create profiles directory if it doesn't exist
        if not os.path.exists(self.profiles_dir):
            os.makedirs(self.profiles_dir)
        
        # Load existing profile or create new one
        if os.path.exists(self.profile_path):
            with open(self.profile_path, 'r') as f:
                return json.load(f)
        else:
            # Create new profile
            new_profile = {
                "username": self.username,
                "watched_movies": [],
                "rated_movies": {},  # {movie_id: rating}
                "genre_preferences": {},  # {genre: weight}
                "favorite_actors": [],
                "favorite_directors": []
            }
            self._save_profile(new_profile)
            return new_profile
    
    def _save_profile(self, profile=None):
        """Save user profile to file"""
        if profile is None:
            profile = self.profile
        
        with open(self.profile_path, 'w') as f:
            json.dump(profile, f, indent=4)
    
    def add_watched_movie(self, movie_data):
        """Add a movie to the user's watched list"""
        # Extract relevant movie data
        movie_info = {
            "id": movie_data.get("id", None),
            "title": movie_data.get("title", ""),
            "genres": movie_data.get("genres", []),
            "director": movie_data.get("director", []),
            "cast": movie_data.get("cast_names", []),
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        # Add to watched movies
        self.profile["watched_movies"].append(movie_info)
        
        # Update genre preferences
        for genre in movie_info["genres"]:
            if genre in self.profile["genre_preferences"]:
                self.profile["genre_preferences"][genre] += 1
            else:
                self.profile["genre_preferences"][genre] = 1
        
        # Update favorite actors and directors
        self._update_favorites(movie_info)
        
        # Save profile
        self._save_profile()
        
        return True
    
    def rate_movie(self, movie_id, title, rating):
        """Rate a movie (1-5 stars)"""
        if rating < 1 or rating > 5:
            return False
        
        # Add to rated movies
        self.profile["rated_movies"][str(movie_id)] = {
            "title": title,
            "rating": rating,
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        # Save profile
        self._save_profile()
        
        return True
    
    def _update_favorites(self, movie_info):
        """Update favorite actors and directors based on watched movies"""
        # Add cast to list for counting
        for actor in movie_info.get("cast", []):
            self.profile["favorite_actors"].append(actor)
        
        # Add directors to list for counting
        for director in movie_info.get("director", []):
            self.profile["favorite_directors"].append(director)
        
        # Keep only top 10 most frequent actors and directors
        actor_counter = Counter(self.profile["favorite_actors"])
        director_counter = Counter(self.profile["favorite_directors"])
        
        self.profile["favorite_actors"] = [actor for actor, _ in actor_counter.most_common(10)]
        self.profile["favorite_directors"] = [director for director, _ in director_counter.most_common(5)]
    
    def get_genre_preferences(self):
        """Get user's genre preferences"""
        return self.profile["genre_preferences"]
    
    def get_favorite_actors(self):
        """Get user's favorite actors"""
        return self.profile["favorite_actors"]
    
    def get_favorite_directors(self):
        """Get user's favorite directors"""
        return self.profile["favorite_directors"]
    
    def get_watched_movies(self):
        """Get user's watched movies"""
        return self.profile["watched_movies"]
    
    def get_rated_movies(self):
        """Get user's rated movies"""
        return self.profile["rated_movies"]
    
    def get_average_rating(self):
        """Get user's average movie rating"""
        if not self.profile["rated_movies"]:
            return 0
        
        ratings = [movie_data["rating"] for movie_data in self.profile["rated_movies"].values()]
        return sum(ratings) / len(ratings)
    
    def get_personalized_recommendations(self, movies_df, top_n=10):
        """
        Generate personalized recommendations based on user profile
        
        This uses a weighted approach considering:
        1. Genre preferences
        2. Favorite actors and directors
        3. Highly rated similar movies
        """
        if not self.profile["watched_movies"]:
            # If no watch history, return popular movies
            return movies_df.sort_values('popularity', ascending=False).head(top_n)
        
        # Create a copy of the movies dataframe
        df = movies_df.copy()
        
        # Initialize personalization score
        df['personalization_score'] = 0.0
        
        # 1. Score based on genre preferences
        genre_weights = self.profile["genre_preferences"]
        total_genre_watches = sum(genre_weights.values()) if genre_weights else 1
        
        def score_by_genre(movie):
            score = 0
            for genre in movie['genres']:
                if genre in genre_weights:
                    # Normalize by total watches
                    score += genre_weights[genre] / total_genre_watches
            return score * 3  # Weight factor for genres
        
        df['genre_score'] = df.apply(score_by_genre, axis=1)
        
        # 2. Score based on favorite actors and directors
        favorite_actors = set(self.profile["favorite_actors"])
        favorite_directors = set(self.profile["favorite_directors"])
        
        def score_by_people(movie):
            score = 0
            # Check for favorite actors
            for actor in movie['cast_names']:
                if actor in favorite_actors:
                    score += 0.5
            
            # Check for favorite directors (weighted higher)
            for director in movie['director']:
                if director in favorite_directors:
                    score += 1.0
            
            return score
        
        df['people_score'] = df.apply(score_by_people, axis=1)
        
        # 3. Exclude already watched movies
        watched_titles = {movie['title'] for movie in self.profile["watched_movies"]}
        df = df[~df['title'].isin(watched_titles)]
        
        # Combine scores
        df['personalization_score'] = df['genre_score'] + df['people_score'] + (df['vote_average'] * 0.2)
        
        # Sort by personalization score and return top N
        return df.sort_values('personalization_score', ascending=False).head(top_n)


class UserProfileManager:
    """Class to manage multiple user profiles"""
    
    def __init__(self, profiles_dir="profiles"):
        """Initialize profile manager"""
        self.profiles_dir = profiles_dir
        self.active_profile = None
        
        # Create profiles directory if it doesn't exist
        if not os.path.exists(profiles_dir):
            os.makedirs(profiles_dir)
    
    def get_all_profiles(self):
        """Get list of all profile usernames"""
        if not os.path.exists(self.profiles_dir):
            return []
        
        profiles = []
        for filename in os.listdir(self.profiles_dir):
            if filename.endswith('.json'):
                profiles.append(filename[:-5])  # Remove .json extension
        
        return profiles
    
    def create_profile(self, username):
        """Create a new user profile"""
        if not username or not isinstance(username, str):
            return False
        
        # Check if profile already exists
        if os.path.exists(os.path.join(self.profiles_dir, f"{username}.json")):
            return False
        
        # Create new profile
        self.active_profile = UserProfile(username, self.profiles_dir)
        return True
    
    def load_profile(self, username):
        """Load an existing user profile"""
        profile_path = os.path.join(self.profiles_dir, f"{username}.json")
        
        if not os.path.exists(profile_path):
            return False
        
        self.active_profile = UserProfile(username, self.profiles_dir)
        return True
    
    def delete_profile(self, username):
        """Delete a user profile"""
        profile_path = os.path.join(self.profiles_dir, f"{username}.json")
        
        if not os.path.exists(profile_path):
            return False
        
        os.remove(profile_path)
        
        if self.active_profile and self.active_profile.username == username:
            self.active_profile = None
        
        return True
    
    def get_active_profile(self):
        """Get the active user profile"""
        return self.active_profile


# Example usage
if __name__ == "__main__":
    # Create profile manager
    manager = UserProfileManager()
    
    # Create a new profile
    manager.create_profile("test_user")
    
    # Get active profile
    profile = manager.get_active_profile()
    
    # Add a watched movie
    movie_data = {
        "id": 123,
        "title": "Test Movie",
        "genres": ["Action", "Adventure"],
        "director": ["Test Director"],
        "cast_names": ["Actor 1", "Actor 2"]
    }
    profile.add_watched_movie(movie_data)
    
    # Rate a movie
    profile.rate_movie(123, "Test Movie", 4)
    
    # Print profile data
    print(f"Username: {profile.username}")
    print(f"Watched Movies: {profile.get_watched_movies()}")
    print(f"Rated Movies: {profile.get_rated_movies()}")
    print(f"Genre Preferences: {profile.get_genre_preferences()}")
    print(f"Favorite Actors: {profile.get_favorite_actors()}")
    print(f"Favorite Directors: {profile.get_favorite_directors()}")