"""
Enhanced Data Manager for Movie Recommender System
Provides caching, unified data loading, and preprocessing capabilities.
"""

import pandas as pd
import numpy as np
import pickle
import os
import ast
import logging
from datetime import datetime, timedelta
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import linear_kernel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataManager:
    """
    Unified data manager that handles loading, preprocessing, and caching of movie data.
    Provides significant performance improvements through intelligent caching.
    """
    
    def __init__(self, cache_dir='cache', cache_expiry_hours=24):
        """
        Initialize the data manager.
        
        Args:
            cache_dir (str): Directory to store cache files
            cache_expiry_hours (int): Hours after which cache expires
        """
        self.cache_dir = cache_dir
        self.cache_expiry_hours = cache_expiry_hours
        self.movies_df = None
        self.cosine_sim = None
        self.indices = None
        self.tfidf_matrix = None
        
        # Create cache directory if it doesn't exist
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created cache directory: {self.cache_dir}")
    
    def _get_cache_path(self, cache_name):
        """Get the full path for a cache file"""
        return os.path.join(self.cache_dir, f"{cache_name}.pkl")
    
    def _is_cache_valid(self, cache_path):
        """Check if cache file exists and is not expired"""
        if not os.path.exists(cache_path):
            return False
        
        # Check if cache is expired
        cache_time = datetime.fromtimestamp(os.path.getmtime(cache_path))
        expiry_time = cache_time + timedelta(hours=self.cache_expiry_hours)
        
        if datetime.now() > expiry_time:
            logger.info(f"Cache expired: {cache_path}")
            return False
        
        return True
    
    def _save_to_cache(self, data, cache_name):
        """Save data to cache file"""
        cache_path = self._get_cache_path(cache_name)
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"Data cached successfully: {cache_name}")
        except Exception as e:
            logger.error(f"Failed to cache data {cache_name}: {str(e)}")
    
    def _load_from_cache(self, cache_name):
        """Load data from cache file"""
        cache_path = self._get_cache_path(cache_name)
        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
            logger.info(f"Data loaded from cache: {cache_name}")
            return data
        except Exception as e:
            logger.error(f"Failed to load cache {cache_name}: {str(e)}")
            return None
    
    def _parse_json_column(self, column_data):
        """Safely parse JSON-like string columns"""
        def parse_item(item):
            try:
                if pd.isna(item) or item == '':
                    return []
                parsed = ast.literal_eval(item)
                if isinstance(parsed, list):
                    return [entry.get('name', '') for entry in parsed if isinstance(entry, dict)]
                return []
            except (ValueError, SyntaxError, AttributeError):
                return []
        
        return column_data.apply(parse_item)
    
    def _preprocess_movies_data(self, movies_df, credits_df):
        """Preprocess and merge movie data"""
        logger.info("Preprocessing movie data...")
        
        # Merge dataframes
        merged_df = movies_df.merge(credits_df, on='title', how='left')
        
        # Parse JSON columns
        merged_df['genres'] = self._parse_json_column(merged_df['genres'])
        merged_df['keywords'] = self._parse_json_column(merged_df['keywords'])
        merged_df['production_companies'] = self._parse_json_column(merged_df['production_companies'])
        merged_df['production_countries'] = self._parse_json_column(merged_df['production_countries'])
        merged_df['spoken_languages'] = self._parse_json_column(merged_df['spoken_languages'])
        
        # Parse cast and crew
        def parse_cast(cast_data):
            try:
                if pd.isna(cast_data):
                    return []
                cast_list = ast.literal_eval(cast_data)
                return [actor.get('name', '') for actor in cast_list[:5]]  # Top 5 actors
            except:
                return []
        
        def parse_crew_for_director(crew_data):
            try:
                if pd.isna(crew_data):
                    return []
                crew_list = ast.literal_eval(crew_data)
                directors = [person.get('name', '') for person in crew_list 
                           if person.get('job') == 'Director']
                return directors
            except:
                return []
        
        merged_df['cast_names'] = merged_df['cast'].apply(parse_cast)
        merged_df['director'] = merged_df['crew'].apply(parse_crew_for_director)
        
        # Create content features for similarity calculation
        def create_content_features(row):
            """Combine all content features into a single string"""
            features = []
            
            # Add genres
            if row['genres']:
                features.extend(row['genres'])
            
            # Add keywords
            if row['keywords']:
                features.extend(row['keywords'])
            
            # Add director
            if row['director']:
                features.extend(row['director'])
            
            # Add top cast
            if row['cast_names']:
                features.extend(row['cast_names'][:3])  # Top 3 actors
            
            return ' '.join(features).lower()
        
        merged_df['content_features'] = merged_df.apply(create_content_features, axis=1)
        
        # Clean data
        merged_df = merged_df.dropna(subset=['title', 'content_features'])
        merged_df = merged_df.drop_duplicates(subset=['title'])
        merged_df = merged_df.reset_index(drop=True)
        
        logger.info(f"Preprocessed {len(merged_df)} movies")
        return merged_df
    
    def _compute_similarity_matrix(self, movies_df):
        """Compute TF-IDF and cosine similarity matrix"""
        logger.info("Computing similarity matrix...")
        
        # Create TF-IDF matrix
        tfidf = TfidfVectorizer(stop_words='english', max_features=5000)
        tfidf_matrix = tfidf.fit_transform(movies_df['content_features'])
        
        # Compute cosine similarity
        cosine_sim = linear_kernel(tfidf_matrix, tfidf_matrix)
        
        # Create indices mapping
        indices = pd.Series(movies_df.index, index=movies_df['title']).drop_duplicates()
        
        logger.info("Similarity matrix computed successfully")
        return tfidf_matrix, cosine_sim, indices
    
    def load_data(self, force_refresh=False):
        """
        Load and preprocess movie data with intelligent caching.
        
        Args:
            force_refresh (bool): Force refresh of cached data
            
        Returns:
            bool: True if data loaded successfully, False otherwise
        """
        try:
            # Check if we can use cached data
            if not force_refresh:
                cached_data = self._load_cached_data()
                if cached_data:
                    self.movies_df, self.cosine_sim, self.indices, self.tfidf_matrix = cached_data
                    logger.info("All data loaded from cache successfully")
                    return True
            
            # Load raw data
            logger.info("Loading raw movie data...")
            movies_df = pd.read_csv('tmdb_5000_movies.csv')
            credits_df = pd.read_csv('tmdb_5000_credits.csv')
            
            # Preprocess data
            self.movies_df = self._preprocess_movies_data(movies_df, credits_df)
            
            # Compute similarity matrix
            self.tfidf_matrix, self.cosine_sim, self.indices = self._compute_similarity_matrix(self.movies_df)
            
            # Cache the processed data
            self._cache_processed_data()
            
            logger.info("Data loading and preprocessing completed successfully")
            return True
            
        except FileNotFoundError as e:
            logger.error(f"Data files not found: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False
    
    def _load_cached_data(self):
        """Load all cached data if available and valid"""
        cache_files = ['movies_df', 'cosine_sim', 'indices', 'tfidf_matrix']
        
        # Check if all cache files are valid
        for cache_name in cache_files:
            if not self._is_cache_valid(self._get_cache_path(cache_name)):
                return None
        
        # Load all cached data
        try:
            movies_df = self._load_from_cache('movies_df')
            cosine_sim = self._load_from_cache('cosine_sim')
            indices = self._load_from_cache('indices')
            tfidf_matrix = self._load_from_cache('tfidf_matrix')
            
            if all(data is not None for data in [movies_df, cosine_sim, indices, tfidf_matrix]):
                return movies_df, cosine_sim, indices, tfidf_matrix
        except Exception as e:
            logger.error(f"Error loading cached data: {str(e)}")
        
        return None
    
    def _cache_processed_data(self):
        """Cache all processed data"""
        self._save_to_cache(self.movies_df, 'movies_df')
        self._save_to_cache(self.cosine_sim, 'cosine_sim')
        self._save_to_cache(self.indices, 'indices')
        self._save_to_cache(self.tfidf_matrix, 'tfidf_matrix')
    
    def clear_cache(self):
        """Clear all cached data"""
        try:
            cache_files = ['movies_df', 'cosine_sim', 'indices', 'tfidf_matrix']
            for cache_name in cache_files:
                cache_path = self._get_cache_path(cache_name)
                if os.path.exists(cache_path):
                    os.remove(cache_path)
            logger.info("Cache cleared successfully")
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
    
    def get_cache_info(self):
        """Get information about cached data"""
        cache_files = ['movies_df', 'cosine_sim', 'indices', 'tfidf_matrix']
        info = {}
        
        for cache_name in cache_files:
            cache_path = self._get_cache_path(cache_name)
            if os.path.exists(cache_path):
                stat = os.stat(cache_path)
                info[cache_name] = {
                    'exists': True,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    'valid': self._is_cache_valid(cache_path)
                }
            else:
                info[cache_name] = {'exists': False}
        
        return info


# Singleton instance for global access
_data_manager_instance = None

def get_data_manager():
    """Get the global data manager instance"""
    global _data_manager_instance
    if _data_manager_instance is None:
        _data_manager_instance = DataManager()
    return _data_manager_instance
